# شاشات الملف الشخصي والإعدادات وإدارة الأدوار - MarketSync

## الميزات المضافة الجديدة

### 1. نظام إدارة الأدوار والصلاحيات 🔐

#### المكونات الجديدة:
- **`Permission`** - نموذج الصلاحيات مع 8 فئات رئيسية
- **`Role`** - نموذج الأدوار مع صلاحيات مخصصة
- **`EnhancedUser`** - نموذج مستخدم محسن مع دعم الأدوار
- **`RoleManagementScreen`** - شاشة إدارة الأدوار والصلاحيات

#### فئات الصلاحيات المدعومة:
```dart
enum PermissionCategory {
  products,    // إدارة المنتجات
  orders,      // إدارة الطلبات  
  customers,   // إدارة العملاء
  inventory,   // إدارة المخزون
  reports,     // التقارير
  settings,    // الإعدادات
  users,       // إدارة المستخدمين
  financial,   // الشؤون المالية
}
```

#### الأدوار المحددة مسبقاً:
- 🔴 **مدير النظام** - صلاحيات كاملة لجميع أجزاء النظام
- 🟣 **المالك** - مالك المتجر مع صلاحيات إدارية واسعة
- 🔵 **المدير** - مدير المتجر مع صلاحيات تشغيلية
- 🟢 **الموظف** - موظف عادي مع صلاحيات أساسية
- 🟠 **مندوب المبيعات** - مركز على أنشطة المبيعات
- 🟡 **المحاسب** - صلاحيات مالية ومحاسبية
- ⚪ **العميل** - عميل خارجي مع صلاحيات محدودة

#### الصلاحيات المتاحة (28 صلاحية):

##### إدارة المنتجات:
- عرض المنتجات
- إضافة المنتجات
- تعديل المنتجات
- حذف المنتجات
- إدارة الفئات

##### إدارة الطلبات:
- عرض الطلبات
- إنشاء الطلبات
- تعديل الطلبات
- إلغاء الطلبات
- تحديث حالة الطلبات

##### إدارة العملاء:
- عرض العملاء
- إضافة العملاء
- تعديل العملاء
- حذف العملاء
- إدارة حدود الائتمان

##### إدارة المخزون:
- عرض المخزون
- تحديث المخزون
- تعديلات المخزون

##### التقارير:
- عرض التقارير
- تصدير التقارير
- التقارير المتقدمة

##### الإعدادات:
- عرض الإعدادات
- تعديل الإعدادات
- النسخ الاحتياطي

##### إدارة المستخدمين:
- عرض المستخدمين
- إضافة المستخدمين
- تعديل المستخدمين
- حذف المستخدمين
- إدارة الأدوار

##### الشؤون المالية:
- عرض الشؤون المالية
- إدارة المدفوعات
- عرض الأرباح والخسائر

### 2. شاشة الملف الشخصي المحسنة 👤

#### الميزات:
- ✅ **رأس الملف الشخصي** مع صورة المستخدم والمعلومات الأساسية
- ✅ **إحصائيات سريعة** (تاريخ الانضمام، آخر نشاط)
- ✅ **المعلومات الشخصية** كاملة مع التحقق
- ✅ **الدور والصلاحيات** مع عرض تفصيلي للصلاحيات
- ✅ **حالة الحساب** (نشط، مؤكد، مصادقة ثنائية)
- ✅ **معلومات النشاط** والتواريخ المهمة
- ✅ **أزرار إجراءات** (تعديل، تغيير كلمة المرور، إعدادات، خروج)

#### المعلومات المعروضة:
```dart
- الاسم الكامل والبريد الإلكتروني
- رقم الهاتف ومعرف المستخدم
- الدور والوصف وعدد الصلاحيات
- حالة التفعيل والتحقق
- تواريخ الإنشاء والتحديث وآخر دخول
- تفاصيل جميع الصلاحيات المتاحة
```

### 3. شاشة الإعدادات الشاملة ⚙️

#### أقسام الإعدادات:

##### المظهر:
- 🎨 **اختيار المظهر** (فاتح، داكن، تلقائي)
- 🌐 **اختيار اللغة** (العربية، الإنجليزية)
- 📝 **حجم الخط** (قريباً)

##### الإشعارات:
- 🔔 **تفعيل الإشعارات** العامة
- 📦 **إشعارات الطلبات** (طلبات جديدة، تحديثات)
- 📋 **إشعارات المنتجات** (مخزون منخفض، منتجات جديدة)
- 👥 **إشعارات العملاء** (عملاء جدد، تجاوز حدود)
- 🔊 **الصوت والاهتزاز** للإشعارات

##### الأمان:
- 🔐 **المصادقة الثنائية** (2FA)
- 👆 **المصادقة البيومترية** (بصمة/وجه)
- 🔑 **تغيير كلمة المرور**
- ⏰ **تسجيل الخروج التلقائي** (15-120 دقيقة)
- 🕐 **انتهاء الجلسة** (60-480 دقيقة)

##### البيانات والمزامنة:
- 🔄 **مزامنة البيانات** التلقائية
- 📱 **الوضع غير المتصل**
- 🧹 **مسح ذاكرة التخزين المؤقت**
- 📤 **تصدير البيانات** الشخصية

##### إعدادات النظام (للمدراء فقط):
- 👥 **إدارة المستخدمين**
- 🔐 **إدارة الأدوار**
- 💾 **النسخ الاحتياطي**
- 📋 **سجلات النظام**

##### حول التطبيق:
- ℹ️ **معلومات التطبيق** (الإصدار، المطور)
- 🆘 **الدعم الفني** (تواصل، ساعات العمل)
- 📄 **الشروط والأحكام**
- 🔒 **سياسة الخصوصية**

##### منطقة الخطر:
- ⚠️ **إعادة تعيين الإعدادات**
- 🗑️ **حذف الحساب** (مع تأكيد مضاعف)

### 4. شاشة إدارة الأدوار والصلاحيات 🛡️

#### تبويب الأدوار:
- ✅ **عرض جميع الأدوار** مع الإحصائيات
- ✅ **إنشاء أدوار مخصصة** جديدة
- ✅ **تعديل الأدوار** الموجودة (غير النظام)
- ✅ **نسخ الأدوار** لإنشاء أدوار مشابهة
- ✅ **حذف الأدوار** المخصصة
- ✅ **عرض تفاصيل الدور** والصلاحيات

#### تبويب الصلاحيات:
- ✅ **عرض الصلاحيات مجمعة حسب الفئة**
- ✅ **وصف تفصيلي لكل صلاحية**
- ✅ **حالة الصلاحية** (نشطة/معطلة)

#### ميزات متقدمة:
- 🎨 **ألوان ورموز مميزة** لكل دور
- 📊 **إحصائيات الأدوار** (عدد الصلاحيات، المستخدمين)
- 🔍 **عرض تفصيلي** لصلاحيات كل دور
- ⚡ **إنشاء سريع** للأدوار من قوالب
- 📋 **نسخ الأدوار** مع تعديل الصلاحيات
- ❓ **مساعدة مدمجة** لفهم النظام

## الملفات المضافة/المحدثة

### الملفات الجديدة:
```
lib/
├── domain/
│   └── entities/
│       ├── permission.dart                    # نموذج الصلاحيات
│       ├── role.dart                         # نموذج الأدوار
│       └── enhanced_user.dart                # نموذج المستخدم المحسن
├── presentation/
│   └── screens/
│       ├── profile/
│       │   └── profile_screen.dart           # شاشة الملف الشخصي
│       ├── settings/
│       │   └── settings_screen.dart          # شاشة الإعدادات
│       └── role_management/
│           └── role_management_screen.dart   # شاشة إدارة الأدوار
```

### الملفات المحدثة:
```
└── lib/presentation/routes/app_router.dart   # إضافة المسارات الجديدة
```

## كيفية الاستخدام

### 1. الملف الشخصي:
1. انتقل إلى `/profile`
2. عرض المعلومات الشخصية والدور
3. اضغط على "عرض جميع الصلاحيات" لرؤية التفاصيل
4. استخدم أزرار الإجراءات للتعديل أو الإعدادات

### 2. الإعدادات:
1. انتقل إلى `/settings`
2. تصفح الأقسام المختلفة
3. قم بتخصيص التفضيلات حسب الحاجة
4. احفظ التغييرات تلقائياً

### 3. إدارة الأدوار (للمدراء):
1. انتقل إلى `/role-management`
2. استخدم تبويب "الأدوار" لإدارة الأدوار
3. استخدم تبويب "الصلاحيات" لعرض التفاصيل
4. أنشئ أدوار مخصصة حسب احتياجات المؤسسة

## التكامل مع النظام

### فحص الصلاحيات:
```dart
// فحص صلاحية واحدة
if (user.hasPermission('view_products')) {
  // عرض المنتجات
}

// فحص عدة صلاحيات
if (user.hasAnyPermission(['add_products', 'edit_products'])) {
  // إظهار زر إدارة المنتجات
}

// فحص جميع الصلاحيات المطلوبة
if (user.hasAllPermissions(['view_reports', 'export_reports'])) {
  // إظهار خيارات التقارير المتقدمة
}
```

### استخدام الأدوار:
```dart
// فحص الدور
if (user.isAdmin) {
  // إظهار إعدادات المدير
}

if (user.isOwner) {
  // إظهار إعدادات المالك
}

// فحص دور محدد
if (user.role.id == 'accountant') {
  // إظهار الميزات المحاسبية
}
```

### إدارة التفضيلات:
```dart
// قراءة التفضيلات
final themeMode = user.getPreference<String>('theme_mode', 'system');
final notificationsEnabled = user.notificationsEnabled;

// تحديث التفضيلات
user = user.updatePreference('theme_mode', 'dark');
user = user.updateSetting('two_factor_enabled', true);
```

## الأمان والحماية

### مستويات الحماية:
1. **التحقق من الصلاحيات** على مستوى الواجهة
2. **التحقق من الأدوار** قبل عرض المحتوى
3. **تشفير البيانات الحساسة** في التفضيلات
4. **مراجعة سجلات النشاط** للتغييرات المهمة

### أفضل الممارسات:
- ✅ فحص الصلاحيات قبل كل إجراء
- ✅ استخدام أدوار محددة بدلاً من صلاحيات فردية
- ✅ تسجيل جميع التغييرات في الأدوار
- ✅ مراجعة دورية للصلاحيات الممنوحة

## الميزات القادمة 🚀

### المرحلة التالية:
- [ ] **إدارة المستخدمين** - إضافة وتعديل المستخدمين
- [ ] **سجلات النشاط** - تتبع جميع الإجراءات
- [ ] **النسخ الاحتياطي** - نسخ واستعادة البيانات
- [ ] **المصادقة الثنائية** - تنفيذ 2FA فعلي
- [ ] **المصادقة البيومترية** - بصمة الإصبع والوجه
- [ ] **إعدادات الشركة** - تخصيص على مستوى المؤسسة
- [ ] **تقارير الصلاحيات** - من يملك ماذا
- [ ] **تدقيق الأمان** - فحص الثغرات الأمنية

## الاختبار

### اختبار الأدوار:
```dart
// اختبار إنشاء دور جديد
final customRole = Role(
  id: 'custom_sales',
  name: 'مندوب مبيعات متقدم',
  description: 'مندوب مبيعات مع صلاحيات إضافية',
  permissions: [
    SystemPermissions.viewProducts,
    SystemPermissions.createOrders,
    SystemPermissions.viewCustomers,
    SystemPermissions.addCustomers,
  ],
  isSystemRole: false,
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);

// اختبار فحص الصلاحيات
assert(customRole.hasPermission('view_products'));
assert(!customRole.hasPermission('delete_products'));
```

### اختبار المستخدم المحسن:
```dart
final user = EnhancedUser(
  id: 'test_user',
  email: '<EMAIL>',
  name: 'مستخدم تجريبي',
  role: SystemRoles.employee,
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
);

// اختبار التفضيلات
final updatedUser = user.updatePreference('theme_mode', 'dark');
assert(updatedUser.themeMode == 'dark');
```

## ملاحظات تقنية

### الأداء:
- فحص الصلاحيات محلياً لسرعة الاستجابة
- تخزين مؤقت للأدوار والصلاحيات
- تحديث تدريجي للواجهات عند تغيير الأدوار

### التوافق:
- يعمل مع جميع أحجام الشاشات
- دعم الاتجاهات المختلفة
- تصميم متجاوب ومتاح

### قابلية التوسع:
- إضافة صلاحيات جديدة بسهولة
- إنشاء فئات صلاحيات مخصصة
- دعم أدوار هجينة ومعقدة

---

تم تطوير هذه الميزات بواسطة Kilo Code لتطبيق MarketSync 🛍️

**إجمالي الميزات المطورة حتى الآن:**
- ✅ إدارة المنتجات مع تحميل الصور ومسح الباركود
- ✅ إدارة الطلبات مع تتبع الحالة والتفاصيل المحسنة  
- ✅ نظام إشعارات شامل ومحلي
- ✅ إدارة الفئات
- ✅ الملف الشخصي والإعدادات الشاملة
- ✅ نظام إدارة الأدوار والصلاحيات المتقدم
- ✅ واجهات مستخدم حديثة ومتجاوبة

التطبيق الآن يحتوي على نظام إدارة مستخدمين متكامل مع أدوار وصلاحيات متقدمة، مما يجعله جاهزاً للاستخدام في المؤسسات الكبيرة! 🎉