import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../constants/app_constants.dart';
import '../../presentation/blocs/theme/theme_bloc.dart';
import '../../presentation/blocs/language/language_bloc.dart';
import '../../presentation/blocs/auth/auth_bloc.dart';
import '../../data/datasources/local/auth_local_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/auth/login_usecase.dart';
import '../../domain/usecases/auth/logout_usecase.dart';
import '../../domain/usecases/auth/check_auth_status_usecase.dart';

/// The service locator instance
final GetIt sl = GetIt.instance;

/// Initialize all dependencies
Future<void> init() async {
  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerSingleton<SharedPreferences>(sharedPreferences);

  //! Core
  
  //! Features - Auth
  // Data sources
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sharedPreferences: sl()),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(localDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(repository: sl()));
  sl.registerLazySingleton(() => LogoutUseCase(repository: sl()));
  sl.registerLazySingleton(() => CheckAuthStatusUseCase(repository: sl()));

  // Blocs
  sl.registerFactory(
    () => AuthBloc(
      loginUseCase: sl(),
      logoutUseCase: sl(),
      checkAuthStatusUseCase: sl(),
    ),
  );

  //! Features - Theme
  sl.registerFactory(
    () => ThemeBloc(
      sharedPreferences: sl(),
      themeModeKey: AppConstants.themeKey,
    ),
  );

  //! Features - Language
  sl.registerFactory(
    () => LanguageBloc(
      sharedPreferences: sl(),
      localeKey: AppConstants.localeKey,
      defaultLocale: const Locale(AppConstants.defaultLocale),
    ),
  );

  // TODO: Add more dependencies as needed
}