/// Base class for all exceptions in the application
class AppException implements Exception {
  final String message;
  
  AppException({required this.message});
  
  @override
  String toString() => message;
}

/// Exception for server errors
class ServerException extends AppException {
  ServerException({required String message}) : super(message: message);
}

/// Exception for cache errors
class CacheException extends AppException {
  CacheException({required String message}) : super(message: message);
}

/// Exception for network connectivity errors
class NetworkException extends AppException {
  NetworkException({required String message}) : super(message: message);
}

/// Exception for authentication errors
class AuthException extends AppException {
  AuthException({required String message}) : super(message: message);
}

/// Exception for validation errors
class ValidationException extends AppException {
  ValidationException({required String message}) : super(message: message);
}

/// Exception for resource not found errors
class NotFoundException extends AppException {
  NotFoundException({required String message}) : super(message: message);
}

/// Exception for permission errors
class PermissionException extends AppException {
  PermissionException({required String message}) : super(message: message);
}