import 'package:equatable/equatable.dart';

/// Abstract class for application failures
abstract class Failure extends Equatable {
  final String message;

  const Failure({required this.message});

  @override
  List<Object> get props => [message];
}

/// Server failures
class ServerFailure extends Failure {
  const ServerFailure({required String message}) : super(message: message);
}

/// Cache failures
class CacheFailure extends Failure {
  const CacheFailure({required String message}) : super(message: message);
}

/// Network failures
class NetworkFailure extends Failure {
  const NetworkFailure({required String message}) : super(message: message);
}

/// Authentication failures
class AuthFailure extends Failure {
  const AuthFailure({required String message}) : super(message: message);
}

/// Validation failures
class ValidationFailure extends Failure {
  const ValidationFailure({required String message}) : super(message: message);
}

/// Database failures
class DatabaseFailure extends Failure {
  const DatabaseFailure({required String message}) : super(message: message);
}

/// Permission failures
class PermissionFailure extends Failure {
  const PermissionFailure({required String message}) : super(message: message);
}

/// Unknown failures
class UnknownFailure extends Failure {
  const UnknownFailure({String message = 'An unknown error occurred'})
      : super(message: message);
}