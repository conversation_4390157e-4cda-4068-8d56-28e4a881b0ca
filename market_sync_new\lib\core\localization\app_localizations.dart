import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'translations/ar_translations.dart';
import 'translations/en_translations.dart';

/// This class handles localization for the app
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  /// The supported locales for the app
  static const List<Locale> supportedLocales = [
    Locale('ar'),
    Locale('en'),
  ];

  /// The localization delegates for the app
  static const List<LocalizationsDelegate> localizationsDelegates = [
    _AppLocalizationsDelegate(),
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];

  /// Helper method to get the current AppLocalizations instance
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  /// The map of localized values
  static final Map<String, Map<String, String>> _localizedValues = {
    'ar': arTranslations,
    'en': enTranslations,
  };

  /// Get a localized string by key
  String translate(String key) {
    return _localizedValues[locale.languageCode]?[key] ?? key;
  }

  // App Title
  String get appTitle => translate('appTitle');

  // Authentication
  String get login => translate('login');
  String get register => translate('register');
  String get email => translate('email');
  String get password => translate('password');
  String get forgotPassword => translate('forgotPassword');
  String get resetPassword => translate('resetPassword');
  String get rememberMe => translate('rememberMe');
  String get logout => translate('logout');

  // Navigation
  String get dashboard => translate('dashboard');
  String get inventory => translate('inventory');
  String get orders => translate('orders');
  String get customers => translate('customers');
  String get products => translate('products');
  String get settings => translate('settings');
  String get profile => translate('profile');
  String get search => translate('search');
  String get notifications => translate('notifications');

  // Dashboard
  String get welcome => translate('welcome');
  String get recentActivity => translate('recentActivity');
  String get viewAll => translate('viewAll');
  String get todaySales => translate('todaySales');
  String get totalProducts => translate('totalProducts');
  String get totalCustomers => translate('totalCustomers');
  String get reports => translate('reports');
  String get analytics => translate('analytics');
  String get sales => translate('sales');
  String get transactions => translate('transactions');
  String get revenue => translate('revenue');
  String get newOrder => translate('newOrder');
  String get orderPlaced => translate('orderPlaced');
  String get lowStock => translate('lowStock');
  String get newCustomer => translate('newCustomer');
  String get paymentReceived => translate('paymentReceived');
  String get tasks => translate('tasks');
  String get shop => translate('shop');

  // Product Management
  String get addProduct => translate('addProduct');
  String get editProduct => translate('editProduct');
  String get deleteProduct => translate('deleteProduct');
  String get productName => translate('productName');
  String get price => translate('price');
  String get quantity => translate('quantity');
  String get category => translate('category');
  String get description => translate('description');
  String get barcode => translate('barcode');
  String get suppliers => translate('suppliers');
  String get inStock => translate('inStock');
  String get outOfStock => translate('outOfStock');
  String get lowStockAlert => translate('lowStockAlert');
  String get uploadImage => translate('uploadImage');

  // Order Management
  String get orderDetails => translate('orderDetails');
  String get orderStatus => translate('orderStatus');
  String get orderDate => translate('orderDate');
  String get orderNumber => translate('orderNumber');
  String get customer => translate('customer');
  String get paymentStatus => translate('paymentStatus');
  String get paymentMethod => translate('paymentMethod');
  String get subtotal => translate('subtotal');
  String get total => translate('total');
  String get deliveryAddress => translate('deliveryAddress');
  String get pending => translate('pending');
  String get processing => translate('processing');
  String get shipped => translate('shipped');
  String get delivered => translate('delivered');
  String get cancelled => translate('cancelled');
  String get paid => translate('paid');
  String get unpaid => translate('unpaid');

  // Customer Management
  String get customerDetails => translate('customerDetails');
  String get contactInfo => translate('contactInfo');
  String get phoneNumber => translate('phoneNumber');
  String get address => translate('address');
  String get addCustomer => translate('addCustomer');
  String get editCustomer => translate('editCustomer');
  String get customerName => translate('customerName');
  String get customerHistory => translate('customerHistory');
  String get creditLimit => translate('creditLimit');
  String get debtAmount => translate('debtAmount');

  // Settings
  String get generalSettings => translate('generalSettings');
  String get language => translate('language');
  String get arabic => translate('arabic');
  String get english => translate('english');
  String get darkMode => translate('darkMode');
  String get lightMode => translate('lightMode');
  String get systemDefault => translate('systemDefault');
  String get notificationSettings => translate('notifications');
  String get accountSettings => translate('accountSettings');
  String get changePassword => translate('changePassword');
  String get updateProfile => translate('updateProfile');

  // Common Actions
  String get save => translate('save');
  String get cancel => translate('cancel');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get add => translate('add');
  String get confirm => translate('confirm');
  String get back => translate('back');
  String get next => translate('next');
  String get done => translate('done');

  // Messages
  String get loading => translate('loading');
  String get error => translate('error');
  String get success => translate('success');
  String get noData => translate('noData');
  String get noResults => translate('noResults');
  String get connectionError => translate('connectionError');
  String get tryAgain => translate('tryAgain');
}

/// Localization delegate
class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}