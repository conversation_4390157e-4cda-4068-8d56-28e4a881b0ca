/// English translations for the app
final Map<String, String> enTranslations = {
  // App Title
  'appTitle': 'Market Sync',

  // Authentication
  'login': 'Login',
  'register': 'Register',
  'email': 'Email',
  'password': 'Password',
  'forgotPassword': 'Forgot Password?',
  'resetPassword': 'Reset Password',
  'rememberMe': 'Remember Me',
  'logout': 'Logout',

  // Navigation
  'dashboard': 'Dashboard',
  'inventory': 'Inventory',
  'orders': 'Orders',
  'customers': 'Customers',
  'products': 'Products',
  'settings': 'Settings',
  'profile': 'Profile',
  'search': 'Search',
  'notifications': 'Notifications',

  // Dashboard
  'welcome': 'Welcome back!',
  'recentActivity': 'Recent Activity',
  'viewAll': 'View All',
  'todaySales': 'Today\'s Sales',
  'totalProducts': 'Total Products',
  'totalCustomers': 'Total Customers',
  'reports': 'Reports',
  'analytics': 'Analytics',
  'sales': 'Sales',
  'transactions': 'Transactions',
  'revenue': 'Revenue',
  'newOrder': 'New Order',
  'orderPlaced': 'Order placed by',
  'lowStock': 'Low Stock Alert',
  'newCustomer': 'New Customer',
  'paymentReceived': 'Payment Received',

  // Product Management
  'addProduct': 'Add Product',
  'editProduct': 'Edit Product',
  'deleteProduct': 'Delete Product',
  'productName': 'Product Name',
  'price': 'Price',
  'quantity': 'Quantity',
  'category': 'Category',
  'description': 'Description',
  'barcode': 'Barcode',
  'suppliers': 'Suppliers',
  'inStock': 'In Stock',
  'outOfStock': 'Out of Stock',
  'lowStockAlert': 'Low Stock Alert',
  'uploadImage': 'Upload Image',

  // Order Management
  'orderDetails': 'Order Details',
  'orderStatus': 'Order Status',
  'orderDate': 'Order Date',
  'orderNumber': 'Order Number',
  'customer': 'Customer',
  'paymentStatus': 'Payment Status',
  'paymentMethod': 'Payment Method',
  'subtotal': 'Subtotal',
  'total': 'Total',
  'deliveryAddress': 'Delivery Address',
  'pending': 'Pending',
  'processing': 'Processing',
  'shipped': 'Shipped',
  'delivered': 'Delivered',
  'cancelled': 'Cancelled',
  'paid': 'Paid',
  'unpaid': 'Unpaid',

  // Customer Management
  'customerDetails': 'Customer Details',
  'contactInfo': 'Contact Information',
  'phoneNumber': 'Phone Number',
  'address': 'Address',
  'addCustomer': 'Add Customer',
  'editCustomer': 'Edit Customer',
  'customerName': 'Customer Name',
  'customerHistory': 'Customer History',
  'creditLimit': 'Credit Limit',
  'debtAmount': 'Debt Amount',

  // Settings
  'generalSettings': 'General Settings',
  'language': 'Language',
  'arabic': 'Arabic',
  'english': 'English',
  'darkMode': 'Dark Mode',
  'lightMode': 'Light Mode',
  'systemDefault': 'System Default',
  'accountSettings': 'Account Settings',
  'changePassword': 'Change Password',
  'updateProfile': 'Update Profile',

  // Common Actions
  'save': 'Save',
  'cancel': 'Cancel',
  'delete': 'Delete',
  'edit': 'Edit',
  'add': 'Add',
  'confirm': 'Confirm',
  'back': 'Back',
  'next': 'Next',
  'done': 'Done',

  // Messages
  'loading': 'Loading...',
  'error': 'Error',
  'success': 'Success',
  'noData': 'No Data',
  'noResults': 'No Results Found',
  'connectionError': 'Connection Error',
  'tryAgain': 'Try Again',
};