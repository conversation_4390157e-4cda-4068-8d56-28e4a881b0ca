import 'package:flutter/foundation.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

/// Service for handling barcode scanning operations
class BarcodeService {
  static final BarcodeService _instance = BarcodeService._internal();
  factory BarcodeService() => _instance;
  BarcodeService._internal();

  /// Validate barcode format
  bool isValidBarcode(String barcode) {
    if (barcode.isEmpty) return false;
    
    // Remove any whitespace
    barcode = barcode.trim();
    
    // Check common barcode formats
    return _isValidEAN13(barcode) ||
           _isValidEAN8(barcode) ||
           _isValidUPCA(barcode) ||
           _isValidUPCE(barcode) ||
           _isValidCode128(barcode) ||
           _isValidCode39(barcode);
  }

  /// Validate EAN-13 barcode (13 digits)
  bool _isValidEAN13(String barcode) {
    if (barcode.length != 13) return false;
    if (!RegExp(r'^\d{13}$').hasMatch(barcode)) return false;
    
    // Calculate check digit
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(barcode[12]);
  }

  /// Validate EAN-8 barcode (8 digits)
  bool _isValidEAN8(String barcode) {
    if (barcode.length != 8) return false;
    if (!RegExp(r'^\d{8}$').hasMatch(barcode)) return false;
    
    // Calculate check digit
    int sum = 0;
    for (int i = 0; i < 7; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit * 3 : digit;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(barcode[7]);
  }

  /// Validate UPC-A barcode (12 digits)
  bool _isValidUPCA(String barcode) {
    if (barcode.length != 12) return false;
    if (!RegExp(r'^\d{12}$').hasMatch(barcode)) return false;
    
    // Calculate check digit
    int sum = 0;
    for (int i = 0; i < 11; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit * 3 : digit;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(barcode[11]);
  }

  /// Validate UPC-E barcode (6 or 8 digits)
  bool _isValidUPCE(String barcode) {
    return (barcode.length == 6 || barcode.length == 8) &&
           RegExp(r'^\d+$').hasMatch(barcode);
  }

  /// Validate Code 128 barcode
  bool _isValidCode128(String barcode) {
    if (barcode.isEmpty) return false;
    // Code 128 can contain ASCII characters 0-127
    return barcode.codeUnits.every((unit) => unit >= 0 && unit <= 127);
  }

  /// Validate Code 39 barcode
  bool _isValidCode39(String barcode) {
    if (barcode.isEmpty) return false;
    // Code 39 supports: 0-9, A-Z, space, and symbols: - . $ / + %
    return RegExp(r'^[0-9A-Z\-\.\$\/\+\% ]+$').hasMatch(barcode);
  }

  /// Get barcode type
  BarcodeType getBarcodeType(String barcode) {
    if (_isValidEAN13(barcode)) return BarcodeType.ean13;
    if (_isValidEAN8(barcode)) return BarcodeType.ean8;
    if (_isValidUPCA(barcode)) return BarcodeType.upcA;
    if (_isValidUPCE(barcode)) return BarcodeType.upcE;
    if (_isValidCode128(barcode)) return BarcodeType.code128;
    if (_isValidCode39(barcode)) return BarcodeType.code39;
    return BarcodeType.unknown;
  }

  /// Format barcode for display
  String formatBarcodeForDisplay(String barcode) {
    final BarcodeType type = getBarcodeType(barcode);
    
    switch (type) {
      case BarcodeType.ean13:
        // Format as: 1 234567 890123
        if (barcode.length == 13) {
          return '${barcode.substring(0, 1)} ${barcode.substring(1, 7)} ${barcode.substring(7, 13)}';
        }
        break;
      case BarcodeType.ean8:
        // Format as: 1234 5678
        if (barcode.length == 8) {
          return '${barcode.substring(0, 4)} ${barcode.substring(4, 8)}';
        }
        break;
      case BarcodeType.upcA:
        // Format as: 1 23456 78901 2
        if (barcode.length == 12) {
          return '${barcode.substring(0, 1)} ${barcode.substring(1, 6)} ${barcode.substring(6, 11)} ${barcode.substring(11, 12)}';
        }
        break;
      default:
        break;
    }
    
    return barcode;
  }

  /// Generate random barcode for testing
  String generateRandomBarcode({BarcodeType type = BarcodeType.ean13}) {
    switch (type) {
      case BarcodeType.ean13:
        return _generateEAN13();
      case BarcodeType.ean8:
        return _generateEAN8();
      case BarcodeType.upcA:
        return _generateUPCA();
      default:
        return _generateEAN13();
    }
  }

  String _generateEAN13() {
    // Generate 12 random digits
    String barcode = '';
    for (int i = 0; i < 12; i++) {
      barcode += (DateTime.now().millisecondsSinceEpoch % 10).toString();
    }
    
    // Calculate and append check digit
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return barcode + checkDigit.toString();
  }

  String _generateEAN8() {
    // Generate 7 random digits
    String barcode = '';
    for (int i = 0; i < 7; i++) {
      barcode += (DateTime.now().millisecondsSinceEpoch % 10).toString();
    }
    
    // Calculate and append check digit
    int sum = 0;
    for (int i = 0; i < 7; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit * 3 : digit;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return barcode + checkDigit.toString();
  }

  String _generateUPCA() {
    // Generate 11 random digits
    String barcode = '';
    for (int i = 0; i < 11; i++) {
      barcode += (DateTime.now().millisecondsSinceEpoch % 10).toString();
    }
    
    // Calculate and append check digit
    int sum = 0;
    for (int i = 0; i < 11; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit * 3 : digit;
    }
    
    int checkDigit = (10 - (sum % 10)) % 10;
    return barcode + checkDigit.toString();
  }

  /// Search product by barcode (placeholder implementation)
  Future<Map<String, dynamic>?> searchProductByBarcode(String barcode) async {
    try {
      // This is a placeholder implementation
      // In a real app, you would call your API or database
      
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      // Return mock product data for demonstration
      if (isValidBarcode(barcode)) {
        return {
          'name': 'Product for barcode $barcode',
          'price': 29.99,
          'description': 'Product found by barcode scan',
          'category': 'Electronics',
          'inStock': true,
        };
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error searching product by barcode: $e');
      }
      return null;
    }
  }

  /// Get barcode information
  Map<String, dynamic> getBarcodeInfo(String barcode) {
    final BarcodeType type = getBarcodeType(barcode);
    final bool isValid = isValidBarcode(barcode);
    final String formatted = formatBarcodeForDisplay(barcode);
    
    return {
      'barcode': barcode,
      'type': type.name,
      'isValid': isValid,
      'formatted': formatted,
      'length': barcode.length,
    };
  }
}

/// Enum for barcode types
enum BarcodeType {
  ean13,
  ean8,
  upcA,
  upcE,
  code128,
  code39,
  unknown,
}

extension BarcodeTypeExtension on BarcodeType {
  String get name {
    switch (this) {
      case BarcodeType.ean13:
        return 'EAN-13';
      case BarcodeType.ean8:
        return 'EAN-8';
      case BarcodeType.upcA:
        return 'UPC-A';
      case BarcodeType.upcE:
        return 'UPC-E';
      case BarcodeType.code128:
        return 'Code 128';
      case BarcodeType.code39:
        return 'Code 39';
      case BarcodeType.unknown:
        return 'Unknown';
    }
  }

  String get description {
    switch (this) {
      case BarcodeType.ean13:
        return '13-digit European Article Number';
      case BarcodeType.ean8:
        return '8-digit European Article Number';
      case BarcodeType.upcA:
        return '12-digit Universal Product Code';
      case BarcodeType.upcE:
        return '6-digit Universal Product Code';
      case BarcodeType.code128:
        return 'High-density linear barcode';
      case BarcodeType.code39:
        return 'Variable length alphanumeric barcode';
      case BarcodeType.unknown:
        return 'Unknown barcode format';
    }
  }
}