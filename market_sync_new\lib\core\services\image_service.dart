import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// Service for handling image operations
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  final ImagePicker _picker = ImagePicker();
  final Uuid _uuid = const Uuid();

  /// Pick image from gallery
  Future<File?> pickImageFromGallery({
    int maxWidth = 1024,
    int maxHeight = 1024,
    int imageQuality = 85,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking image from gallery: $e');
      }
      rethrow;
    }
  }

  /// Pick image from camera
  Future<File?> pickImageFromCamera({
    int maxWidth = 1024,
    int maxHeight = 1024,
    int imageQuality = 85,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error taking photo: $e');
      }
      rethrow;
    }
  }

  /// Save image to local storage
  Future<String?> saveImageLocally(File imageFile, {String? customName}) async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String imagesDir = '${appDir.path}/images';
      
      // Create images directory if it doesn't exist
      final Directory imagesDirObj = Directory(imagesDir);
      if (!await imagesDirObj.exists()) {
        await imagesDirObj.create(recursive: true);
      }
      
      // Generate unique filename
      final String fileName = customName ?? '${_uuid.v4()}.jpg';
      final String filePath = '$imagesDir/$fileName';
      
      // Copy file to local storage
      final File savedFile = await imageFile.copy(filePath);
      
      return savedFile.path;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving image locally: $e');
      }
      return null;
    }
  }

  /// Delete image from local storage
  Future<bool> deleteImageLocally(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      if (await imageFile.exists()) {
        await imageFile.delete();
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      return false;
    }
  }

  /// Get image size in bytes
  Future<int> getImageSize(File imageFile) async {
    try {
      return await imageFile.length();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting image size: $e');
      }
      return 0;
    }
  }

  /// Compress image if it's too large
  Future<File?> compressImageIfNeeded(
    File imageFile, {
    int maxSizeInBytes = 2 * 1024 * 1024, // 2MB
    int quality = 85,
  }) async {
    try {
      final int currentSize = await getImageSize(imageFile);
      
      if (currentSize <= maxSizeInBytes) {
        return imageFile; // No compression needed
      }
      
      // Calculate new quality based on size ratio
      final double sizeRatio = maxSizeInBytes / currentSize;
      final int newQuality = (quality * sizeRatio).round().clamp(10, 100);
      
      // Re-pick with lower quality
      final Uint8List imageBytes = await imageFile.readAsBytes();
      
      // For now, return original file
      // In a real implementation, you'd use image compression library
      return imageFile;
    } catch (e) {
      if (kDebugMode) {
        print('Error compressing image: $e');
      }
      return imageFile;
    }
  }

  /// Upload image to server (placeholder implementation)
  Future<String?> uploadImageToServer(File imageFile) async {
    try {
      // This is a placeholder implementation
      // In a real app, you would upload to your server or cloud storage
      
      await Future.delayed(const Duration(seconds: 2)); // Simulate upload time
      
      // Return a mock URL
      return 'https://example.com/uploads/${_uuid.v4()}.jpg';
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      return null;
    }
  }

  /// Get cached images directory
  Future<Directory> getCachedImagesDirectory() async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final Directory cacheDir = Directory('${appDir.path}/cached_images');
    
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    
    return cacheDir;
  }

  /// Clear cached images
  Future<void> clearCachedImages() async {
    try {
      final Directory cacheDir = await getCachedImagesDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing cached images: $e');
      }
    }
  }

  /// Get total size of cached images
  Future<int> getCachedImagesSize() async {
    try {
      final Directory cacheDir = await getCachedImagesDirectory();
      if (!await cacheDir.exists()) {
        return 0;
      }
      
      int totalSize = 0;
      await for (final FileSystemEntity entity in cacheDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      
      return totalSize;
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating cached images size: $e');
      }
      return 0;
    }
  }

  /// Format file size for display
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Validate image file
  bool isValidImageFile(File file) {
    final String extension = file.path.toLowerCase().split('.').last;
    const List<String> validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return validExtensions.contains(extension);
  }

  /// Get image file extension
  String getImageExtension(File file) {
    return file.path.split('.').last.toLowerCase();
  }
}