import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

/// Service for handling local notifications
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permissions for iOS
    if (Platform.isIOS) {
      await _requestIOSPermissions();
    }

    _isInitialized = true;
  }

  /// Request iOS permissions
  Future<void> _requestIOSPermissions() async {
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final String? payload = notificationResponse.payload;
    if (payload != null) {
      if (kDebugMode) {
        print('Notification tapped with payload: $payload');
      }
      // Handle navigation based on payload
      _handleNotificationNavigation(payload);
    }
  }

  /// Handle navigation based on notification payload
  void _handleNotificationNavigation(String payload) {
    // Parse payload and navigate accordingly
    // Format: "type:id" (e.g., "order:123", "product:456")
    final parts = payload.split(':');
    if (parts.length == 2) {
      final type = parts[0];
      final id = parts[1];
      
      switch (type) {
        case 'order':
          // Navigate to order details
          break;
        case 'product':
          // Navigate to product details
          break;
        case 'customer':
          // Navigate to customer details
          break;
        default:
          break;
      }
    }
  }

  /// Show a simple notification
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.defaultPriority,
  }) async {
    if (!_isInitialized) await initialize();

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'market_sync_channel',
      'MarketSync Notifications',
      channelDescription: 'Notifications for MarketSync app',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  /// Show order notification
  Future<void> showOrderNotification({
    required String orderId,
    required String customerName,
    required OrderNotificationType type,
    double? amount,
  }) async {
    String title;
    String body;
    
    switch (type) {
      case OrderNotificationType.newOrder:
        title = 'طلب جديد 🛒';
        body = 'طلب جديد من $customerName';
        if (amount != null) {
          body += ' بقيمة \$${amount.toStringAsFixed(2)}';
        }
        break;
      case OrderNotificationType.orderUpdated:
        title = 'تحديث الطلب 📝';
        body = 'تم تحديث طلب $customerName';
        break;
      case OrderNotificationType.orderShipped:
        title = 'تم الشحن 🚚';
        body = 'تم شحن طلب $customerName';
        break;
      case OrderNotificationType.orderDelivered:
        title = 'تم التسليم ✅';
        body = 'تم تسليم طلب $customerName بنجاح';
        break;
      case OrderNotificationType.orderCancelled:
        title = 'طلب ملغي ❌';
        body = 'تم إلغاء طلب $customerName';
        break;
      case OrderNotificationType.paymentReceived:
        title = 'دفعة مستلمة 💰';
        body = 'تم استلام دفعة من $customerName';
        if (amount != null) {
          body += ' بقيمة \$${amount.toStringAsFixed(2)}';
        }
        break;
    }

    await showNotification(
      id: orderId.hashCode,
      title: title,
      body: body,
      payload: 'order:$orderId',
    );
  }

  /// Show product notification
  Future<void> showProductNotification({
    required String productId,
    required String productName,
    required ProductNotificationType type,
    int? stockQuantity,
  }) async {
    String title;
    String body;
    
    switch (type) {
      case ProductNotificationType.lowStock:
        title = 'مخزون منخفض ⚠️';
        body = 'المنتج "$productName" مخزونه منخفض';
        if (stockQuantity != null) {
          body += ' ($stockQuantity متبقي)';
        }
        break;
      case ProductNotificationType.outOfStock:
        title = 'نفد المخزون 🚫';
        body = 'المنتج "$productName" نفد من المخزون';
        break;
      case ProductNotificationType.productAdded:
        title = 'منتج جديد ➕';
        body = 'تم إضافة المنتج "$productName"';
        break;
      case ProductNotificationType.priceChanged:
        title = 'تغيير السعر 💲';
        body = 'تم تغيير سعر المنتج "$productName"';
        break;
    }

    await showNotification(
      id: productId.hashCode,
      title: title,
      body: body,
      payload: 'product:$productId',
    );
  }

  /// Show customer notification
  Future<void> showCustomerNotification({
    required String customerId,
    required String customerName,
    required CustomerNotificationType type,
    double? amount,
  }) async {
    String title;
    String body;
    
    switch (type) {
      case CustomerNotificationType.newCustomer:
        title = 'عميل جديد 👤';
        body = 'انضم عميل جديد: $customerName';
        break;
      case CustomerNotificationType.creditLimitExceeded:
        title = 'تجاوز حد الائتمان ⚠️';
        body = 'العميل $customerName تجاوز حد الائتمان';
        break;
      case CustomerNotificationType.paymentOverdue:
        title = 'دفعة متأخرة 📅';
        body = 'دفعة متأخرة للعميل $customerName';
        if (amount != null) {
          body += ' بقيمة \$${amount.toStringAsFixed(2)}';
        }
        break;
    }

    await showNotification(
      id: customerId.hashCode,
      title: title,
      body: body,
      payload: 'customer:$customerId',
    );
  }

  /// Show scheduled notification
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_isInitialized) await initialize();

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'market_sync_scheduled',
      'MarketSync Scheduled',
      channelDescription: 'Scheduled notifications for MarketSync app',
      importance: Importance.max,
      priority: Priority.high,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      platformChannelSpecifics,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  /// Cancel notification
  Future<void> cancelNotification(int id) async {
    await _flutterLocalNotificationsPlugin.cancel(id);
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  /// Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.areNotificationsEnabled() ?? false;
    } else if (Platform.isIOS) {
      final IOSFlutterLocalNotificationsPlugin? iosImplementation =
          _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>();
      // For iOS, we'll assume notifications are enabled if we can get the implementation
      return iosImplementation != null;
    }
    return false;
  }
}

/// Order notification types
enum OrderNotificationType {
  newOrder,
  orderUpdated,
  orderShipped,
  orderDelivered,
  orderCancelled,
  paymentReceived,
}

/// Product notification types
enum ProductNotificationType {
  lowStock,
  outOfStock,
  productAdded,
  priceChanged,
}

/// Customer notification types
enum CustomerNotificationType {
  newCustomer,
  creditLimitExceeded,
  paymentOverdue,
}

/// Notification priority levels
enum NotificationPriority {
  min,
  low,
  defaultPriority,
  high,
  max,
}