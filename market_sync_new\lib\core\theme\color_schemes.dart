import 'package:flutter/material.dart';

/// Light theme color scheme
final lightColorScheme = ColorScheme(
  brightness: Brightness.light,
  primary: const Color(0xFF007BFF),
  onPrimary: Colors.white,
  primaryContainer: const Color(0xFFD7E9FF),
  onPrimaryContainer: const Color(0xFF001D36),
  secondary: const Color(0xFF6C757D),
  onSecondary: Colors.white,
  secondaryContainer: const Color(0xFFE2E6E9),
  onSecondaryContainer: const Color(0xFF22272B),
  tertiary: const Color(0xFF28A745),
  onTertiary: Colors.white,
  tertiaryContainer: const Color(0xFFD4EDDA),
  onTertiaryContainer: const Color(0xFF0A3319),
  error: const Color(0xFFDC3545),
  onError: Colors.white,
  errorContainer: const Color(0xFFF8D7DA),
  onErrorContainer: const Color(0xFF491017),
  background: const Color(0xFFF8F9FA),
  onBackground: const Color(0xFF212529),
  surface: Colors.white,
  onSurface: const Color(0xFF212529),
  surfaceVariant: const Color(0xFFE9ECEF),
  onSurfaceVariant: const Color(0xFF495057),
  outline: const Color(0xFFADB5BD),
  outlineVariant: const Color(0xFFDEE2E6),
  shadow: const Color(0xFF000000),
  scrim: const Color(0xFF000000),
  inverseSurface: const Color(0xFF212529),
  onInverseSurface: const Color(0xFFF8F9FA),
  inversePrimary: const Color(0xFF9ECAFF),
);

/// Dark theme color scheme
final darkColorScheme = ColorScheme(
  brightness: Brightness.dark,
  primary: const Color(0xFF3D9CFF),
  onPrimary: Colors.white,
  primaryContainer: const Color(0xFF004D87),
  onPrimaryContainer: const Color(0xFFD7E9FF),
  secondary: const Color(0xFF8D959D),
  onSecondary: Colors.black,
  secondaryContainer: const Color(0xFF404850),
  onSecondaryContainer: const Color(0xFFE2E6E9),
  tertiary: const Color(0xFF4DD968),
  onTertiary: Colors.black,
  tertiaryContainer: const Color(0xFF0A5A2A),
  onTertiaryContainer: const Color(0xFFD4EDDA),
  error: const Color(0xFFFF596A),
  onError: Colors.black,
  errorContainer: const Color(0xFF93242E),
  onErrorContainer: const Color(0xFFF8D7DA),
  background: const Color(0xFF121212),
  onBackground: const Color(0xFFE9ECEF),
  surface: const Color(0xFF1E1E1E),
  onSurface: const Color(0xFFE9ECEF),
  surfaceVariant: const Color(0xFF343A40),
  onSurfaceVariant: const Color(0xFFADB5BD),
  outline: const Color(0xFF6C757D),
  outlineVariant: const Color(0xFF495057),
  shadow: const Color(0xFF000000),
  scrim: const Color(0xFF000000),
  inverseSurface: const Color(0xFFF8F9FA),
  onInverseSurface: const Color(0xFF212529),
  inversePrimary: const Color(0xFF0062B1),
);