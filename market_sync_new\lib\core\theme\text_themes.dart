import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'color_schemes.dart';

/// Returns the appropriate text theme based on whether dark mode is enabled
TextTheme getTextTheme(bool isDark) {
  final ColorScheme colorScheme = isDark ? darkColorScheme : lightColorScheme;
  
  return TextTheme(
    displayLarge: TextStyle(
      fontSize: 32.sp,
      fontWeight: FontWeight.bold,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    displayMedium: TextStyle(
      fontSize: 28.sp,
      fontWeight: FontWeight.bold,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    displaySmall: TextStyle(
      fontSize: 24.sp,
      fontWeight: FontWeight.bold,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    headlineLarge: TextStyle(
      fontSize: 22.sp,
      fontWeight: FontWeight.w600,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    headlineMedium: TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w600,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    headlineSmall: TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w600,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    titleLarge: TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w600,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    titleMedium: TextStyle(
      fontSize: 15.sp,
      fontWeight: FontWeight.w500,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    titleSmall: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    bodyLarge: TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.normal,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    bodyMedium: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.normal,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    bodySmall: TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.normal,
      color: colorScheme.onBackground.withOpacity(0.8),
      fontFamily: 'Cairo',
    ),
    labelLarge: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    labelMedium: TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      color: colorScheme.onBackground,
      fontFamily: 'Cairo',
    ),
    labelSmall: TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w500,
      color: colorScheme.onBackground.withOpacity(0.8),
      fontFamily: 'Cairo',
    ),
  );
}