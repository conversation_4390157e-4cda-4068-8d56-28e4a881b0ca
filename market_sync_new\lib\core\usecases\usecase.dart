import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../error/failures.dart';

/// Abstract class for use cases that take a parameter and return a result
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// Abstract class for use cases that don't require any parameters
abstract class NoParamsUseCase<Type> {
  Future<Either<Failure, Type>> call();
}

/// No parameters class for use cases that don't require any parameters
class NoParams extends Equatable {
  @override
  List<Object> get props => [];
}