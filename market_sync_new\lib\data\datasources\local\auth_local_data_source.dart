import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/failures.dart';
import '../../models/user_model.dart';

/// Abstract class for local authentication data operations
abstract class AuthLocalDataSource {
  /// Get the cached user data
  Future<UserModel> getCachedUser();

  /// Cache the user data
  Future<void> cacheUser(UserModel user);

  /// Clear the cached user data
  Future<void> clearCachedUser();

  /// Check if a user is cached
  Future<bool> isUserCached();
}

/// Implementation of [AuthLocalDataSource]
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences sharedPreferences;

  AuthLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<UserModel> getCachedUser() async {
    final jsonString = sharedPreferences.getString(AppConstants.userKey);
    
    if (jsonString != null) {
      return UserModel.fromJson(json.decode(jsonString));
    } else {
      throw const CacheFailure(message: 'No cached user found');
    }
  }

  @override
  Future<void> cacheUser(UserModel user) async {
    await sharedPreferences.setString(
      AppConstants.userKey,
      json.encode(user.toJson()),
    );
  }

  @override
  Future<void> clearCachedUser() async {
    await sharedPreferences.remove(AppConstants.userKey);
    await sharedPreferences.remove(AppConstants.tokenKey);
  }

  @override
  Future<bool> isUserCached() async {
    return sharedPreferences.containsKey(AppConstants.userKey);
  }
}