import 'package:hive/hive.dart';

import '../../../domain/entities/category.dart';
import '../../../domain/entities/order.dart';
import '../../../domain/entities/product.dart';
import '../../../domain/entities/transaction.dart';
import '../../../domain/entities/user.dart';

/// Initialize Hive adapters
void initHiveAdapters() {
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(UserRoleAdapter());
  Hive.registerAdapter(ProductAdapter());
  Hive.registerAdapter(CategoryAdapter());
  Hive.registerAdapter(OrderAdapter());
  Hive.registerAdapter(OrderItemAdapter());
  Hive.registerAdapter(OrderStatusAdapter());
  Hive.registerAdapter(TransactionAdapter());
  Hive.registerAdapter(TransactionTypeAdapter());
  Hive.registerAdapter(PaymentMethodAdapter());
}

/// Adapter for UserRole enum
class UserRoleAdapter extends TypeAdapter<UserRole> {
  @override
  final int typeId = 0;

  @override
  UserRole read(BinaryReader reader) {
    return UserRole.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, UserRole obj) {
    writer.writeByte(obj.index);
  }
}

/// Adapter for User entity
class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 1;

  @override
  User read(BinaryReader reader) {
    final Map<String, dynamic> fields = {};
    final numOfFields = reader.readByte();
    
    for (var i = 0; i < numOfFields; i++) {
      final key = reader.readString();
      final value = reader.read();
      fields[key] = value;
    }
    
    return User(
      id: fields['id'] as String,
      name: fields['name'] as String,
      email: fields['email'] as String,
      phoneNumber: fields['phoneNumber'] as String?,
      role: fields['role'] as UserRole,
      imageUrl: fields['imageUrl'] as String?,
      isActive: fields['isActive'] as bool,
      createdAt: fields['createdAt'] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer.writeByte(8);
    writer.writeString('id');
    writer.write(obj.id);
    writer.writeString('name');
    writer.write(obj.name);
    writer.writeString('email');
    writer.write(obj.email);
    writer.writeString('phoneNumber');
    writer.write(obj.phoneNumber);
    writer.writeString('role');
    writer.write(obj.role);
    writer.writeString('imageUrl');
    writer.write(obj.imageUrl);
    writer.writeString('isActive');
    writer.write(obj.isActive);
    writer.writeString('createdAt');
    writer.write(obj.createdAt);
  }
}

/// Adapter for Category entity
class CategoryAdapter extends TypeAdapter<Category> {
  @override
  final int typeId = 2;

  @override
  Category read(BinaryReader reader) {
    final Map<String, dynamic> fields = {};
    final numOfFields = reader.readByte();
    
    for (var i = 0; i < numOfFields; i++) {
      final key = reader.readString();
      final value = reader.read();
      fields[key] = value;
    }
    
    return Category(
      id: fields['id'] as String,
      name: fields['name'] as String,
      description: fields['description'] as String?,
      imageUrl: fields['imageUrl'] as String?,
      isActive: fields['isActive'] as bool,
      createdAt: fields['createdAt'] as DateTime,
      updatedAt: fields['updatedAt'] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, Category obj) {
    writer.writeByte(7);
    writer.writeString('id');
    writer.write(obj.id);
    writer.writeString('name');
    writer.write(obj.name);
    writer.writeString('description');
    writer.write(obj.description);
    writer.writeString('imageUrl');
    writer.write(obj.imageUrl);
    writer.writeString('isActive');
    writer.write(obj.isActive);
    writer.writeString('createdAt');
    writer.write(obj.createdAt);
    writer.writeString('updatedAt');
    writer.write(obj.updatedAt);
  }
}

/// Adapter for Product entity
class ProductAdapter extends TypeAdapter<Product> {
  @override
  final int typeId = 3;

  @override
  Product read(BinaryReader reader) {
    final Map<String, dynamic> fields = {};
    final numOfFields = reader.readByte();
    
    for (var i = 0; i < numOfFields; i++) {
      final key = reader.readString();
      final value = reader.read();
      fields[key] = value;
    }
    
    return Product(
      id: fields['id'] as String,
      name: fields['name'] as String,
      description: fields['description'] as String,
      price: fields['price'] as double,
      discountPrice: fields['discountPrice'] as double?,
      stockQuantity: fields['stockQuantity'] as int,
      categoryId: fields['categoryId'] as String,
      imageUrl: fields['imageUrl'] as String?,
      barcode: fields['barcode'] as String,
      isActive: fields['isActive'] as bool,
      createdAt: fields['createdAt'] as DateTime,
      updatedAt: fields['updatedAt'] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, Product obj) {
    writer.writeByte(12);
    writer.writeString('id');
    writer.write(obj.id);
    writer.writeString('name');
    writer.write(obj.name);
    writer.writeString('description');
    writer.write(obj.description);
    writer.writeString('price');
    writer.write(obj.price);
    writer.writeString('discountPrice');
    writer.write(obj.discountPrice);
    writer.writeString('stockQuantity');
    writer.write(obj.stockQuantity);
    writer.writeString('categoryId');
    writer.write(obj.categoryId);
    writer.writeString('imageUrl');
    writer.write(obj.imageUrl);
    writer.writeString('barcode');
    writer.write(obj.barcode);
    writer.writeString('isActive');
    writer.write(obj.isActive);
    writer.writeString('createdAt');
    writer.write(obj.createdAt);
    writer.writeString('updatedAt');
    writer.write(obj.updatedAt);
  }
}

/// Adapter for OrderStatus enum
class OrderStatusAdapter extends TypeAdapter<OrderStatus> {
  @override
  final int typeId = 4;

  @override
  OrderStatus read(BinaryReader reader) {
    return OrderStatus.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, OrderStatus obj) {
    writer.writeByte(obj.index);
  }
}

/// Adapter for OrderItem entity
class OrderItemAdapter extends TypeAdapter<OrderItem> {
  @override
  final int typeId = 5;

  @override
  OrderItem read(BinaryReader reader) {
    final Map<String, dynamic> fields = {};
    final numOfFields = reader.readByte();
    
    for (var i = 0; i < numOfFields; i++) {
      final key = reader.readString();
      final value = reader.read();
      fields[key] = value;
    }
    
    return OrderItem(
      id: fields['id'] as String,
      productId: fields['productId'] as String,
      productName: fields['productName'] as String,
      price: fields['price'] as double,
      quantity: fields['quantity'] as int,
      total: fields['total'] as double,
      product: fields['product'] as Product?,
    );
  }

  @override
  void write(BinaryWriter writer, OrderItem obj) {
    writer.writeByte(7);
    writer.writeString('id');
    writer.write(obj.id);
    writer.writeString('productId');
    writer.write(obj.productId);
    writer.writeString('productName');
    writer.write(obj.productName);
    writer.writeString('price');
    writer.write(obj.price);
    writer.writeString('quantity');
    writer.write(obj.quantity);
    writer.writeString('total');
    writer.write(obj.total);
    writer.writeString('product');
    writer.write(obj.product);
  }
}

/// Adapter for Order entity
class OrderAdapter extends TypeAdapter<Order> {
  @override
  final int typeId = 6;

  @override
  Order read(BinaryReader reader) {
    final Map<String, dynamic> fields = {};
    final numOfFields = reader.readByte();
    
    for (var i = 0; i < numOfFields; i++) {
      final key = reader.readString();
      final value = reader.read();
      fields[key] = value;
    }
    
    return Order(
      id: fields['id'] as String,
      customerId: fields['customerId'] as String,
      customerName: fields['customerName'] as String?,
      items: (fields['items'] as List).cast<OrderItem>(),
      subtotal: fields['subtotal'] as double,
      tax: fields['tax'] as double,
      shipping: fields['shipping'] as double,
      discount: fields['discount'] as double,
      total: fields['total'] as double,
      status: fields['status'] as OrderStatus,
      notes: fields['notes'] as String?,
      orderDate: fields['orderDate'] as DateTime,
      shippedDate: fields['shippedDate'] as DateTime?,
      deliveryDate: fields['deliveryDate'] as DateTime?,
      customer: fields['customer'] as User?,
    );
  }

  @override
  void write(BinaryWriter writer, Order obj) {
    writer.writeByte(15);
    writer.writeString('id');
    writer.write(obj.id);
    writer.writeString('customerId');
    writer.write(obj.customerId);
    writer.writeString('customerName');
    writer.write(obj.customerName);
    writer.writeString('items');
    writer.write(obj.items);
    writer.writeString('subtotal');
    writer.write(obj.subtotal);
    writer.writeString('tax');
    writer.write(obj.tax);
    writer.writeString('shipping');
    writer.write(obj.shipping);
    writer.writeString('discount');
    writer.write(obj.discount);
    writer.writeString('total');
    writer.write(obj.total);
    writer.writeString('status');
    writer.write(obj.status);
    writer.writeString('notes');
    writer.write(obj.notes);
    writer.writeString('orderDate');
    writer.write(obj.orderDate);
    writer.writeString('shippedDate');
    writer.write(obj.shippedDate);
    writer.writeString('deliveryDate');
    writer.write(obj.deliveryDate);
    writer.writeString('customer');
    writer.write(obj.customer);
  }
}

/// Adapter for TransactionType enum
class TransactionTypeAdapter extends TypeAdapter<TransactionType> {
  @override
  final int typeId = 7;

  @override
  TransactionType read(BinaryReader reader) {
    return TransactionType.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, TransactionType obj) {
    writer.writeByte(obj.index);
  }
}

/// Adapter for PaymentMethod enum
class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 8;

  @override
  PaymentMethod read(BinaryReader reader) {
    return PaymentMethod.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    writer.writeByte(obj.index);
  }
}

/// Adapter for Transaction entity
class TransactionAdapter extends TypeAdapter<Transaction> {
  @override
  final int typeId = 9;

  @override
  Transaction read(BinaryReader reader) {
    final Map<String, dynamic> fields = {};
    final numOfFields = reader.readByte();
    
    for (var i = 0; i < numOfFields; i++) {
      final key = reader.readString();
      final value = reader.read();
      fields[key] = value;
    }
    
    return Transaction(
      id: fields['id'] as String,
      userId: fields['userId'] as String,
      orderId: fields['orderId'] as String?,
      type: fields['type'] as TransactionType,
      paymentMethod: fields['paymentMethod'] as PaymentMethod,
      amount: fields['amount'] as double,
      referenceNumber: fields['referenceNumber'] as String?,
      notes: fields['notes'] as String?,
      isSuccess: fields['isSuccess'] as bool,
      transactionDate: fields['transactionDate'] as DateTime,
      order: fields['order'] as Order?,
    );
  }

  @override
  void write(BinaryWriter writer, Transaction obj) {
    writer.writeByte(11);
    writer.writeString('id');
    writer.write(obj.id);
    writer.writeString('userId');
    writer.write(obj.userId);
    writer.writeString('orderId');
    writer.write(obj.orderId);
    writer.writeString('type');
    writer.write(obj.type);
    writer.writeString('paymentMethod');
    writer.write(obj.paymentMethod);
    writer.writeString('amount');
    writer.write(obj.amount);
    writer.writeString('referenceNumber');
    writer.write(obj.referenceNumber);
    writer.writeString('notes');
    writer.write(obj.notes);
    writer.writeString('isSuccess');
    writer.write(obj.isSuccess);
    writer.writeString('transactionDate');
    writer.write(obj.transactionDate);
    writer.writeString('order');
    writer.write(obj.order);
  }
}