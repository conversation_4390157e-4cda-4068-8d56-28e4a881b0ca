import 'dart:async';

import 'package:hive/hive.dart';

import '../../../domain/entities/order.dart';

abstract class OrderLocalDataSource {
  /// Get all orders from local storage
  Future<List<Order>> getOrders();

  /// Get an order by its ID
  Future<Order?> getOrderById(String id);

  /// Get orders by customer ID
  Future<List<Order>> getOrdersByCustomerId(String customerId);

  /// Save an order to local storage
  Future<void> saveOrder(Order order);

  /// Save multiple orders to local storage
  Future<void> saveOrders(List<Order> orders);

  /// Delete an order from local storage
  Future<void> deleteOrder(String id);

  /// Update order status
  Future<void> updateOrderStatus(String id, OrderStatus status);

  /// Get orders by status
  Future<List<Order>> getOrdersByStatus(OrderStatus status);

  /// Get orders by date range
  Future<List<Order>> getOrdersByDateRange(
    DateTime startDate,
    DateTime endDate,
  );

  /// Search orders
  Future<List<Order>> searchOrders(String query);
}

class OrderLocalDataSourceImpl implements OrderLocalDataSource {
  final Box<Order> orderBox;

  OrderLocalDataSourceImpl({
    required this.orderBox,
  });

  @override
  Future<List<Order>> getOrders() async {
    return orderBox.values.toList();
  }

  @override
  Future<Order?> getOrderById(String id) async {
    return orderBox.get(id);
  }

  @override
  Future<List<Order>> getOrdersByCustomerId(String customerId) async {
    return orderBox.values
        .where((order) => order.customerId == customerId)
        .toList();
  }

  @override
  Future<void> saveOrder(Order order) async {
    await orderBox.put(order.id, order);
  }

  @override
  Future<void> saveOrders(List<Order> orders) async {
    final Map<String, Order> ordersMap = {
      for (var order in orders) order.id: order
    };
    await orderBox.putAll(ordersMap);
  }

  @override
  Future<void> deleteOrder(String id) async {
    await orderBox.delete(id);
  }

  @override
  Future<void> updateOrderStatus(String id, OrderStatus status) async {
    final order = await getOrderById(id);
    if (order != null) {
      final updatedOrder = order.copyWith(status: status);
      await saveOrder(updatedOrder);
    }
  }

  @override
  Future<List<Order>> getOrdersByStatus(OrderStatus status) async {
    return orderBox.values
        .where((order) => order.status == status)
        .toList();
  }

  @override
  Future<List<Order>> getOrdersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Set time to midnight for proper date comparison
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
    
    return orderBox.values
        .where((order) =>
            order.orderDate.isAfter(start) &&
            order.orderDate.isBefore(end))
        .toList();
  }

  @override
  Future<List<Order>> searchOrders(String query) async {
    final lowercaseQuery = query.toLowerCase();
    return orderBox.values
        .where((order) =>
            order.id.toLowerCase().contains(lowercaseQuery) ||
            (order.customerName?.toLowerCase().contains(lowercaseQuery) ?? false) ||
            order.items.any((item) =>
                item.productName.toLowerCase().contains(lowercaseQuery)))
        .toList();
  }
}