import 'dart:async';

import 'package:hive/hive.dart';

import '../../../domain/entities/product.dart';
import '../../../domain/entities/category.dart';

abstract class ProductLocalDataSource {
  /// Get all products from local storage
  Future<List<Product>> getProducts();

  /// Get a product by its ID
  Future<Product?> getProductById(String id);

  /// Get products by category ID
  Future<List<Product>> getProductsByCategoryId(String categoryId);

  /// Save a product to local storage
  Future<void> saveProduct(Product product);

  /// Save multiple products to local storage
  Future<void> saveProducts(List<Product> products);

  /// Delete a product from local storage
  Future<void> deleteProduct(String id);

  /// Search products by name
  Future<List<Product>> searchProducts(String query);

  /// Get products with low stock
  Future<List<Product>> getLowStockProducts(int threshold);

  /// Get all categories from local storage
  Future<List<Category>> getCategories();

  /// Get a category by its ID
  Future<Category?> getCategoryById(String id);

  /// Save a category to local storage
  Future<void> saveCategory(Category category);

  /// Save multiple categories to local storage
  Future<void> saveCategories(List<Category> categories);

  /// Delete a category from local storage
  Future<void> deleteCategory(String id);
}

class ProductLocalDataSourceImpl implements ProductLocalDataSource {
  final Box<Product> productBox;
  final Box<Category> categoryBox;

  ProductLocalDataSourceImpl({
    required this.productBox,
    required this.categoryBox,
  });

  @override
  Future<List<Product>> getProducts() async {
    return productBox.values.toList();
  }

  @override
  Future<Product?> getProductById(String id) async {
    return productBox.get(id);
  }

  @override
  Future<List<Product>> getProductsByCategoryId(String categoryId) async {
    return productBox.values
        .where((product) => product.categoryId == categoryId)
        .toList();
  }

  @override
  Future<void> saveProduct(Product product) async {
    await productBox.put(product.id, product);
  }

  @override
  Future<void> saveProducts(List<Product> products) async {
    final Map<String, Product> productsMap = {
      for (var product in products) product.id: product
    };
    await productBox.putAll(productsMap);
  }

  @override
  Future<void> deleteProduct(String id) async {
    await productBox.delete(id);
  }

  @override
  Future<List<Product>> searchProducts(String query) async {
    final lowercaseQuery = query.toLowerCase();
    return productBox.values
        .where((product) =>
            product.name.toLowerCase().contains(lowercaseQuery) ||
            product.description.toLowerCase().contains(lowercaseQuery) ||
            product.barcode.toLowerCase().contains(lowercaseQuery))
        .toList();
  }

  @override
  Future<List<Product>> getLowStockProducts(int threshold) async {
    return productBox.values
        .where((product) => product.stockQuantity <= threshold)
        .toList();
  }

  @override
  Future<List<Category>> getCategories() async {
    return categoryBox.values.toList();
  }

  @override
  Future<Category?> getCategoryById(String id) async {
    return categoryBox.get(id);
  }

  @override
  Future<void> saveCategory(Category category) async {
    await categoryBox.put(category.id, category);
  }

  @override
  Future<void> saveCategories(List<Category> categories) async {
    final Map<String, Category> categoriesMap = {
      for (var category in categories) category.id: category
    };
    await categoryBox.putAll(categoriesMap);
  }

  @override
  Future<void> deleteCategory(String id) async {
    await categoryBox.delete(id);
  }
}