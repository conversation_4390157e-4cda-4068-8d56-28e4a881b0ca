import 'package:supabase_flutter/supabase_flutter.dart' hide AuthException;

import '../../../core/config/supabase_config.dart';
import '../../../core/error/exceptions.dart';
import '../../models/user_model.dart';

abstract class AuthRemoteDataSource {
  /// Sign in with email and password
  Future<UserModel> signIn(String email, String password);
  
  /// Sign out
  Future<void> signOut();
  
  /// Get the current user
  Future<UserModel?> getCurrentUser();
  
  /// Register with email and password
  Future<UserModel> register(UserModel user, String password);
  
  /// Reset password
  Future<void> resetPassword(String email);
  
  /// Update user
  Future<UserModel> updateUser(UserModel user);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient _supabaseClient;
  
  AuthRemoteDataSourceImpl({SupabaseClient? supabaseClient}) 
      : _supabaseClient = supabaseClient ?? Supabase.instance.client;
  
  @override
  Future<UserModel> signIn(String email, String password) async {
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user == null) {
        throw AuthException(message: 'Failed to sign in');
      }
      
      final userId = response.user!.id;
      
      // Fetch additional user data from users table
      final userData = await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .select()
          .eq('id', userId)
          .single();
      
      return UserModel.fromJson(userData);
    } on AuthException catch (e) {
      throw AuthException(message: e.message);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<void> signOut() async {
    try {
      await _supabaseClient.auth.signOut();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      
      if (user == null) {
        return null;
      }
      
      // Fetch additional user data from users table
      final userData = await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .select()
          .eq('id', user.id)
          .maybeSingle();
      
      if (userData == null) {
        return null;
      }
      
      return UserModel.fromJson(userData);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<UserModel> register(UserModel user, String password) async {
    try {
      // First create auth user
      final response = await _supabaseClient.auth.signUp(
        email: user.email,
        password: password,
      );
      
      if (response.user == null) {
        throw AuthException(message: 'Failed to register user');
      }
      
      final newUser = user.copyWithModel(id: response.user!.id);
      
      // Then save user data to users table
      await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .insert(newUser.toJson());
      
      return newUser;
    } on AuthException catch (e) {
      throw AuthException(message: e.message);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<void> resetPassword(String email) async {
    try {
      await _supabaseClient.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<UserModel> updateUser(UserModel user) async {
    try {
      await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .update(user.toJson())
          .eq('id', user.id);
      
      return user;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}