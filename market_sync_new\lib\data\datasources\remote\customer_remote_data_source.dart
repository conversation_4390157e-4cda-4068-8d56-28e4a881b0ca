import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../core/config/supabase_config.dart';
import '../../../core/error/exceptions.dart';
import '../../models/user_model.dart';

abstract class CustomerRemoteDataSource {
  /// Get all customers
  Future<List<UserModel>> getCustomers();
  
  /// Get a customer by ID
  Future<UserModel> getCustomerById(String id);
  
  /// Create a new customer
  Future<UserModel> createCustomer(UserModel customer);
  
  /// Update an existing customer
  Future<UserModel> updateCustomer(UserModel customer);
  
  /// Delete a customer
  Future<bool> deleteCustomer(String id);
  
  /// Search customers by query
  Future<List<UserModel>> searchCustomers(String query);
  
  /// Get customers with outstanding balances
  Future<List<UserModel>> getCustomersWithOutstandingBalances();
  
  /// Update customer credit limit
  Future<bool> updateCustomerCreditLimit(String customerId, double creditLimit);
  
  /// Get customer credit balance
  Future<double> getCustomerCreditBalance(String customerId);
}

class CustomerRemoteDataSourceImpl implements CustomerRemoteDataSource {
  final SupabaseClient _supabaseClient;
  
  CustomerRemoteDataSourceImpl({SupabaseClient? supabaseClient}) 
      : _supabaseClient = supabaseClient ?? Supabase.instance.client;
  
  @override
  Future<List<UserModel>> getCustomers() async {
    try {
      final response = await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .select()
          .or('role.eq.customer,role.eq.wholesaler')
          .order('name');
      
      return (response as List)
          .map((json) => UserModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<UserModel> getCustomerById(String id) async {
    try {
      final response = await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .select()
          .eq('id', id)
          .single();
      
      return UserModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<UserModel> createCustomer(UserModel customer) async {
    try {
      // Generate a unique ID if not provided
      final customerWithId = customer.id.isEmpty 
          ? customer.copyWithModel(id: DateTime.now().millisecondsSinceEpoch.toString())
          : customer;
          
      await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .insert(customerWithId.toJson());
      
      return customerWithId;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<UserModel> updateCustomer(UserModel customer) async {
    try {
      await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .update(customer.toJson())
          .eq('id', customer.id);
      
      return customer;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<bool> deleteCustomer(String id) async {
    try {
      await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .delete()
          .eq('id', id);
      
      return true;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<List<UserModel>> searchCustomers(String query) async {
    try {
      final lowercaseQuery = query.toLowerCase();
      
      final response = await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .select()
          .or('role.eq.customer,role.eq.wholesaler')
          .or('name.ilike.%$lowercaseQuery%,email.ilike.%$lowercaseQuery%,phone_number.ilike.%$lowercaseQuery%')
          .order('name');
      
      return (response as List)
          .map((json) => UserModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<List<UserModel>> getCustomersWithOutstandingBalances() async {
    try {
      // This is a placeholder implementation
      // In a real application, you would need to join with a transactions or balances table
      final response = await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .select()
          .or('role.eq.customer,role.eq.wholesaler')
          .order('name');
      
      return (response as List)
          .map((json) => UserModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<bool> updateCustomerCreditLimit(String customerId, double creditLimit) async {
    try {
      // This is a placeholder implementation
      // In a real application, you would need a dedicated credit_limits table
      await _supabaseClient
          .from(SupabaseConfig.usersTable)
          .update({'credit_limit': creditLimit})
          .eq('id', customerId);
      
      return true;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
  
  @override
  Future<double> getCustomerCreditBalance(String customerId) async {
    try {
      // This is a placeholder implementation
      // In a real application, you would calculate this from transactions
      return 0.0;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}