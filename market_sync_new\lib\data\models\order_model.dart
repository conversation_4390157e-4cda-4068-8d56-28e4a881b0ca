import '../../domain/entities/order.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/user.dart';
import 'user_model.dart';

/// OrderItem model for API serialization/deserialization
class OrderItemModel extends OrderItem {
  const OrderItemModel({
    required super.id,
    required super.productId,
    required super.productName,
    required super.price,
    required super.quantity,
    required super.total,
    super.product,
  });

  /// Create an OrderItemModel from JSON data
  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    return OrderItemModel(
      id: json['id'] as String,
      productId: json['product_id'] as String,
      productName: json['product_name'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: json['quantity'] as int,
      total: (json['total'] as num).toDouble(),
      // Note: product is set separately if needed
    );
  }

  /// Convert this OrderItemModel to a JSON map
  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'price': price,
      'quantity': quantity,
      'total': total,
      // Note: product is not included in JSON
    };
  }

  /// Create an OrderItemModel from a domain entity
  factory OrderItemModel.fromEntity(OrderItem orderItem) {
    return OrderItemModel(
      id: orderItem.id,
      productId: orderItem.productId,
      productName: orderItem.productName,
      price: orderItem.price,
      quantity: orderItem.quantity,
      total: orderItem.total,
      product: orderItem.product,
    );
  }
}

/// Order model for API serialization/deserialization
class OrderModel extends Order {
  const OrderModel({
    required super.id,
    required super.customerId,
    super.customerName,
    required super.items,
    required super.subtotal,
    required super.tax,
    required super.shipping,
    required super.discount,
    required super.total,
    required super.status,
    super.notes,
    required super.orderDate,
    super.shippedDate,
    super.deliveryDate,
    super.customer,
  });

  /// Create an OrderModel from JSON data
  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] as String,
      customerId: json['customer_id'] as String,
      customerName: json['customer_name'] as String?,
      items: (json['items'] as List<dynamic>)
          .map((item) => OrderItemModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      subtotal: (json['subtotal'] as num).toDouble(),
      tax: (json['tax'] as num).toDouble(),
      shipping: (json['shipping'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => OrderStatus.pending,
      ),
      notes: json['notes'] as String?,
      orderDate: DateTime.parse(json['order_date'] as String),
      shippedDate: json['shipped_date'] != null
          ? DateTime.parse(json['shipped_date'] as String)
          : null,
      deliveryDate: json['delivery_date'] != null
          ? DateTime.parse(json['delivery_date'] as String)
          : null,
      // Note: customer is set separately if needed
    );
  }

  /// Convert this OrderModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'items': items.map((item) {
        if (item is OrderItemModel) {
          return item.toJson();
        }
        return OrderItemModel.fromEntity(item).toJson();
      }).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'discount': discount,
      'total': total,
      'status': status.toString().split('.').last,
      'notes': notes,
      'order_date': orderDate.toIso8601String(),
      'shipped_date': shippedDate?.toIso8601String(),
      'delivery_date': deliveryDate?.toIso8601String(),
      // Note: customer is not included in JSON
    };
  }

  /// Create an OrderModel from a domain entity
  factory OrderModel.fromEntity(Order order) {
    return OrderModel(
      id: order.id,
      customerId: order.customerId,
      customerName: order.customerName,
      items: order.items.map((item) {
        if (item is OrderItemModel) {
          return item;
        }
        return OrderItemModel.fromEntity(item);
      }).toList(),
      subtotal: order.subtotal,
      tax: order.tax,
      shipping: order.shipping,
      discount: order.discount,
      total: order.total,
      status: order.status,
      notes: order.notes,
      orderDate: order.orderDate,
      shippedDate: order.shippedDate,
      deliveryDate: order.deliveryDate,
      customer: order.customer,
    );
  }
}