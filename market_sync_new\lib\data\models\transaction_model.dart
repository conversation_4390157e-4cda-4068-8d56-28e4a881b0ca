import '../../domain/entities/order.dart';
import '../../domain/entities/transaction.dart';
import 'order_model.dart';

/// Transaction model for API serialization/deserialization
class TransactionModel extends Transaction {
  const TransactionModel({
    required super.id,
    required super.userId,
    super.orderId,
    required super.type,
    required super.paymentMethod,
    required super.amount,
    super.referenceNumber,
    super.notes,
    required super.isSuccess,
    required super.transactionDate,
    super.order,
  });

  /// Create a TransactionModel from JSON data
  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      orderId: json['order_id'] as String?,
      type: TransactionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => TransactionType.sale,
      ),
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['payment_method'],
        orElse: () => PaymentMethod.cash,
      ),
      amount: (json['amount'] as num).toDouble(),
      referenceNumber: json['reference_number'] as String?,
      notes: json['notes'] as String?,
      isSuccess: json['is_success'] as bool,
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      // Note: order is set separately if needed
    );
  }

  /// Convert this TransactionModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'order_id': orderId,
      'type': type.toString().split('.').last,
      'payment_method': paymentMethod.toString().split('.').last,
      'amount': amount,
      'reference_number': referenceNumber,
      'notes': notes,
      'is_success': isSuccess,
      'transaction_date': transactionDate.toIso8601String(),
      // Note: order is not included in JSON
    };
  }

  /// Create a TransactionModel from a domain entity
  factory TransactionModel.fromEntity(Transaction transaction) {
    return TransactionModel(
      id: transaction.id,
      userId: transaction.userId,
      orderId: transaction.orderId,
      type: transaction.type,
      paymentMethod: transaction.paymentMethod,
      amount: transaction.amount,
      referenceNumber: transaction.referenceNumber,
      notes: transaction.notes,
      isSuccess: transaction.isSuccess,
      transactionDate: transaction.transactionDate,
      order: transaction.order,
    );
  }
}