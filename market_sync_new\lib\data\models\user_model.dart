import '../../domain/entities/user.dart';

/// Model class for [User] entity
class UserModel extends User {
  const UserModel({
    required super.id,
    required super.name,
    required super.email,
    super.phoneNumber,
    required super.role,
    super.imageUrl,
    required super.isActive,
    required super.createdAt,
  });

  /// Create a UserModel from json
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phoneNumber: json['phone_number'],
      role: _mapStringToUserRole(json['role']),
      imageUrl: json['image_url'],
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  /// Convert UserModel to json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone_number': phoneNumber,
      'role': role.toString().split('.').last,
      'image_url': imageUrl,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Map string to UserRole enum
  static UserRole _mapStringToUserRole(String roleStr) {
    switch (roleStr) {
      case 'owner':
        return UserRole.owner;
      case 'employee':
        return UserRole.employee;
      case 'customer':
        return UserRole.customer;
      case 'wholesaler':
        return UserRole.wholesaler;
      case 'agent':
        return UserRole.agent;
      case 'admin':
        return UserRole.admin;
      default:
        return UserRole.customer;
    }
  }
  
  /// Create an empty UserModel
  factory UserModel.empty() {
    return UserModel(
      id: '',
      name: '',
      email: '',
      role: UserRole.customer,
      isActive: true,
      createdAt: DateTime.now(),
    );
  }
  
  /// Create a copy of this UserModel with given fields replaced
  UserModel copyWithModel({
    String? id,
    String? name,
    String? email,
    String? phoneNumber,
    UserRole? role,
    String? imageUrl,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}