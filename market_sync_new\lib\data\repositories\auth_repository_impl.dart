import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/local/auth_local_data_source.dart';
import '../models/user_model.dart';

/// Implementation of the AuthRepository
class AuthRepositoryImpl implements AuthRepository {
  final AuthLocalDataSource localDataSource;

  AuthRepositoryImpl({required this.localDataSource});

  @override
  Future<Either<Failure, User>> checkAuthStatus() async {
    try {
      final isUserCached = await localDataSource.isUserCached();
      
      if (isUserCached) {
        final user = await localDataSource.getCachedUser();
        return Right(user);
      } else {
        return const Left(AuthFailure(message: 'User not authenticated'));
      }
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      // TODO: Implement actual API login
      // This is a mock implementation for now
      if (email == '<EMAIL>' && password == 'password123') {
        final user = UserModel(
          id: '1',
          name: 'Admin User',
          email: email,
          phoneNumber: '+1234567890',
          role: UserRole.owner,
          imageUrl: null,
          isActive: true,
          createdAt: DateTime.now(),
        );
        
        await localDataSource.cacheUser(user);
        return Right(user);
      } else {
        return const Left(AuthFailure(message: 'Invalid email or password'));
      }
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await localDataSource.clearCachedUser();
      return const Right(null);
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> register({
    required String name,
    required String email,
    required String password,
    String? phoneNumber,
  }) async {
    try {
      // TODO: Implement actual API registration
      // This is a mock implementation for now
      final user = UserModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        email: email,
        phoneNumber: phoneNumber,
        role: UserRole.customer,
        imageUrl: null,
        isActive: true,
        createdAt: DateTime.now(),
      );
      
      await localDataSource.cacheUser(user);
      return Right(user);
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> updateProfile({
    required String name,
    String? phoneNumber,
    String? imageUrl,
  }) async {
    try {
      // TODO: Implement actual API profile update
      // This is a mock implementation for now
      final currentUser = await localDataSource.getCachedUser();
      
      final updatedUser = UserModel(
        id: currentUser.id,
        name: name,
        email: currentUser.email,
        phoneNumber: phoneNumber ?? currentUser.phoneNumber,
        role: currentUser.role,
        imageUrl: imageUrl ?? currentUser.imageUrl,
        isActive: currentUser.isActive,
        createdAt: currentUser.createdAt,
      );
      
      await localDataSource.cacheUser(updatedUser);
      return Right(updatedUser);
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }
}