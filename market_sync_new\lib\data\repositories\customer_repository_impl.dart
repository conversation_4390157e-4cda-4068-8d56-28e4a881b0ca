import 'package:dartz/dartz.dart';

import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/customer_repository.dart';
import '../datasources/remote/customer_remote_data_source.dart';
import '../models/user_model.dart';

/// Implementation of CustomerRepository
class CustomerRepositoryImpl implements CustomerRepository {
  final CustomerRemoteDataSource remoteDataSource;
  
  CustomerRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, List<User>>> getCustomers() async {
    try {
      final customers = await remoteDataSource.getCustomers();
      return Right(customers);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> getCustomerById(String id) async {
    try {
      final customer = await remoteDataSource.getCustomerById(id);
      return Right(customer);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> createCustomer(User customer) async {
    try {
      // Convert User entity to UserModel
      final customerModel = customer is UserModel
          ? customer
          : UserModel(
              id: customer.id,
              name: customer.name,
              email: customer.email,
              phoneNumber: customer.phoneNumber,
              role: customer.role,
              imageUrl: customer.imageUrl,
              isActive: customer.isActive,
              createdAt: customer.createdAt,
            );
      
      final createdCustomer = await remoteDataSource.createCustomer(customerModel);
      return Right(createdCustomer);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, User>> updateCustomer(User customer) async {
    try {
      // Convert User entity to UserModel
      final customerModel = customer is UserModel
          ? customer
          : UserModel(
              id: customer.id,
              name: customer.name,
              email: customer.email,
              phoneNumber: customer.phoneNumber,
              role: customer.role,
              imageUrl: customer.imageUrl,
              isActive: customer.isActive,
              createdAt: customer.createdAt,
            );
      
      final updatedCustomer = await remoteDataSource.updateCustomer(customerModel);
      return Right(updatedCustomer);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteCustomer(String id) async {
    try {
      final result = await remoteDataSource.deleteCustomer(id);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<User>>> searchCustomers(String query) async {
    try {
      final customers = await remoteDataSource.searchCustomers(query);
      return Right(customers);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<User>>> getCustomersWithOutstandingBalances() async {
    try {
      final customers = await remoteDataSource.getCustomersWithOutstandingBalances();
      return Right(customers);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateCustomerCreditLimit(
    String customerId,
    double creditLimit,
  ) async {
    try {
      final result = await remoteDataSource.updateCustomerCreditLimit(
        customerId,
        creditLimit,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, double>> getCustomerCreditBalance(String customerId) async {
    try {
      final balance = await remoteDataSource.getCustomerCreditBalance(customerId);
      return Right(balance);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, Map<String, dynamic>>> getCustomerStatistics(String customerId) async {
    try {
      // This is a placeholder implementation
      // In a real application, you would fetch statistics from the backend
      return const Right({
        'totalOrders': 0,
        'totalSpent': 0.0,
        'averageOrderValue': 0.0,
        'lastOrderDate': null,
      });
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, bool>> addCustomerCredit(
    String customerId,
    double amount,
    String reason,
  ) async {
    try {
      // This is a placeholder implementation
      // In a real application, you would add a transaction and update the balance
      return const Right(true);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, List<dynamic>>> getCustomerOrderHistory(String customerId) async {
    try {
      // This is a placeholder implementation
      // In a real application, you would fetch the customer's order history
      return const Right([]);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}