import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../../core/error/exceptions.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/category.dart';
import '../../domain/repositories/product_repository.dart';
import '../datasources/local/product_local_data_source.dart';

/// Implementation of [ProductRepository]
class ProductRepositoryImpl implements ProductRepository {
  final ProductLocalDataSource localDataSource;
  
  ProductRepositoryImpl({required this.localDataSource});
  
  @override
  Future<Either<Failure, List<Product>>> getProducts() async {
    try {
      final products = await localDataSource.getProducts();
      return Right(products);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, Product>> getProductById(String id) async {
    try {
      final product = await localDataSource.getProductById(id);
      if (product != null) {
        return Right(product);
      } else {
        return Left(CacheFailure(message: 'Product not found'));
      }
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, List<Product>>> getProductsByCategoryId(String categoryId) async {
    try {
      final products = await localDataSource.getProductsByCategoryId(categoryId);
      return Right(products);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, Product>> createProduct(Product product) async {
    try {
      await localDataSource.saveProduct(product);
      return Right(product);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, Product>> updateProduct(Product product) async {
    try {
      await localDataSource.saveProduct(product);
      return Right(product);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, bool>> deleteProduct(String id) async {
    try {
      await localDataSource.deleteProduct(id);
      return const Right(true);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, List<Product>>> searchProducts(String query) async {
    try {
      final products = await localDataSource.searchProducts(query);
      return Right(products);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, List<Product>>> getLowStockProducts(int threshold) async {
    try {
      final products = await localDataSource.getLowStockProducts(threshold);
      return Right(products);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, List<Category>>> getCategories() async {
    try {
      final categories = await localDataSource.getCategories();
      return Right(categories);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, Category>> getCategoryById(String id) async {
    try {
      final category = await localDataSource.getCategoryById(id);
      if (category != null) {
        return Right(category);
      } else {
        return Left(CacheFailure(message: 'Category not found'));
      }
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, Category>> createCategory(Category category) async {
    try {
      await localDataSource.saveCategory(category);
      return Right(category);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, Category>> updateCategory(Category category) async {
    try {
      await localDataSource.saveCategory(category);
      return Right(category);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
  
  @override
  Future<Either<Failure, bool>> deleteCategory(String id) async {
    try {
      await localDataSource.deleteCategory(id);
      return const Right(true);
    } on Exception catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }
}