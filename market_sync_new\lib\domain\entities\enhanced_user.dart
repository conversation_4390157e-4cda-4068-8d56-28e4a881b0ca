import 'role.dart';
import 'permission.dart';

/// Enhanced user entity with role-based access control
class EnhancedUser {
  final String id;
  final String email;
  final String name;
  final String? phone;
  final String? avatar;
  final Role role;
  final bool isActive;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic> preferences;
  final Map<String, dynamic> settings;

  const EnhancedUser({
    required this.id,
    required this.email,
    required this.name,
    this.phone,
    this.avatar,
    required this.role,
    this.isActive = true,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.preferences = const {},
    this.settings = const {},
  });

  /// Check if user has a specific permission
  bool hasPermission(String permissionId) {
    return role.hasPermission(permissionId);
  }

  /// Check if user has any permission from a list
  bool hasAnyPermission(List<String> permissionIds) {
    return role.hasAnyPermission(permissionIds);
  }

  /// Check if user has all permissions from a list
  bool hasAllPermissions(List<String> permissionIds) {
    return role.hasAllPermissions(permissionIds);
  }

  /// Check if user can perform a specific action
  bool canPerform(String action) {
    return hasPermission(action);
  }

  /// Get user's display name with fallback
  String get displayName {
    if (name.isNotEmpty) return name;
    if (email.isNotEmpty) return email.split('@').first;
    return 'مستخدم';
  }

  /// Get user's initials for avatar
  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty && parts[0].isNotEmpty) {
      return parts[0][0].toUpperCase();
    }
    return 'U';
  }

  /// Check if user is internal (employee, manager, etc.)
  bool get isInternal {
    return role.id != 'customer';
  }

  /// Check if user is external (customer)
  bool get isExternal {
    return role.id == 'customer';
  }

  /// Check if user is admin level
  bool get isAdmin {
    return role.id == 'super_admin' || role.id == 'owner';
  }

  /// Check if user is owner
  bool get isOwner {
    return role.id == 'owner' || role.id == 'super_admin';
  }

  /// Check if user is manager level
  bool get isManager {
    return role.id == 'manager' || isAdmin;
  }

  /// Check if user is employee level
  bool get isEmployee {
    return role.id == 'employee' || isManager;
  }

  /// Check if user is customer
  bool get isCustomer {
    return role.id == 'customer';
  }

  /// Get user preference value
  T? getPreference<T>(String key, [T? defaultValue]) {
    return preferences[key] as T? ?? defaultValue;
  }

  /// Get user setting value
  T? getSetting<T>(String key, [T? defaultValue]) {
    return settings[key] as T? ?? defaultValue;
  }

  /// Get theme preference
  String get themeMode {
    return getPreference<String>('theme_mode', 'system') ?? 'system';
  }

  /// Get language preference
  String get language {
    return getPreference<String>('language', 'ar') ?? 'ar';
  }

  /// Get notification preferences
  bool get notificationsEnabled {
    return getPreference<bool>('notifications_enabled', true) ?? true;
  }

  bool get orderNotificationsEnabled {
    return getPreference<bool>('order_notifications', true) ?? true;
  }

  bool get productNotificationsEnabled {
    return getPreference<bool>('product_notifications', true) ?? true;
  }

  bool get customerNotificationsEnabled {
    return getPreference<bool>('customer_notifications', false) ?? false;
  }

  /// Get security settings
  bool get twoFactorEnabled {
    return getSetting<bool>('two_factor_enabled', false) ?? false;
  }

  bool get biometricEnabled {
    return getSetting<bool>('biometric_enabled', false) ?? false;
  }

  /// Copy user with updated fields
  EnhancedUser copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? avatar,
    Role? role,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? settings,
  }) {
    return EnhancedUser(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      settings: settings ?? this.settings,
    );
  }

  /// Update preferences
  EnhancedUser updatePreferences(Map<String, dynamic> newPreferences) {
    final updatedPreferences = Map<String, dynamic>.from(preferences);
    updatedPreferences.addAll(newPreferences);
    return copyWith(
      preferences: updatedPreferences,
      updatedAt: DateTime.now(),
    );
  }

  /// Update settings
  EnhancedUser updateSettings(Map<String, dynamic> newSettings) {
    final updatedSettings = Map<String, dynamic>.from(settings);
    updatedSettings.addAll(newSettings);
    return copyWith(
      settings: updatedSettings,
      updatedAt: DateTime.now(),
    );
  }

  /// Update single preference
  EnhancedUser updatePreference(String key, dynamic value) {
    return updatePreferences({key: value});
  }

  /// Update single setting
  EnhancedUser updateSetting(String key, dynamic value) {
    return updateSettings({key: value});
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EnhancedUser &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// User profile information for display
class UserProfile {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? avatar;
  final String roleName;
  final String department;
  final DateTime joinDate;
  final bool isOnline;
  final DateTime? lastSeen;

  const UserProfile({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.avatar,
    required this.roleName,
    required this.department,
    required this.joinDate,
    this.isOnline = false,
    this.lastSeen,
  });

  /// Create profile from enhanced user
  factory UserProfile.fromEnhancedUser(EnhancedUser user) {
    return UserProfile(
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      avatar: user.avatar,
      roleName: user.role.name,
      department: _getDepartmentFromRole(user.role.id),
      joinDate: user.createdAt,
      isOnline: _isUserOnline(user.lastLoginAt),
      lastSeen: user.lastLoginAt,
    );
  }

  static String _getDepartmentFromRole(String roleId) {
    switch (roleId) {
      case 'super_admin':
      case 'owner':
        return 'الإدارة العليا';
      case 'manager':
        return 'الإدارة';
      case 'employee':
        return 'الموظفين';
      case 'sales_rep':
        return 'المبيعات';
      case 'accountant':
        return 'المحاسبة';
      case 'customer':
        return 'العملاء';
      default:
        return 'غير محدد';
    }
  }

  static bool _isUserOnline(DateTime? lastLogin) {
    if (lastLogin == null) return false;
    final now = DateTime.now();
    final difference = now.difference(lastLogin);
    return difference.inMinutes < 15; // Consider online if last login was within 15 minutes
  }
}