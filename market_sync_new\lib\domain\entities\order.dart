import 'package:equatable/equatable.dart';

import 'product.dart';
import 'user.dart';

/// Order status enum
enum OrderStatus {
  pending,
  processing,
  shipped,
  delivered,
  cancelled,
  returned,
}

/// Order item representing a product in an order
class OrderItem extends Equatable {
  final String id;
  final String productId;
  final String productName;
  final double price;
  final int quantity;
  final double total;
  final Product? product; // For detailed product information

  const OrderItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
    required this.total,
    this.product,
  });

  /// Creates a copy of this order item with the given fields replaced
  OrderItem copyWith({
    String? id,
    String? productId,
    String? productName,
    double? price,
    int? quantity,
    double? total,
    Product? product,
  }) {
    return OrderItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      total: total ?? this.total,
      product: product ?? this.product,
    );
  }

  @override
  List<Object?> get props => [
        id,
        productId,
        productName,
        price,
        quantity,
        total,
        product,
      ];
}

/// Order entity representing a customer order
class Order extends Equatable {
  final String id;
  final String customerId;
  final String? customerName;
  final List<OrderItem> items;
  final double subtotal;
  final double tax;
  final double shipping;
  final double discount;
  final double total;
  final OrderStatus status;
  final String? notes;
  final DateTime orderDate;
  final DateTime? shippedDate;
  final DateTime? deliveryDate;
  final User? customer; // For detailed customer information

  const Order({
    required this.id,
    required this.customerId,
    this.customerName,
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.discount,
    required this.total,
    required this.status,
    this.notes,
    required this.orderDate,
    this.shippedDate,
    this.deliveryDate,
    this.customer,
  });

  /// Creates a copy of this order with the given fields replaced
  Order copyWith({
    String? id,
    String? customerId,
    String? customerName,
    List<OrderItem>? items,
    double? subtotal,
    double? tax,
    double? shipping,
    double? discount,
    double? total,
    OrderStatus? status,
    String? notes,
    DateTime? orderDate,
    DateTime? shippedDate,
    DateTime? deliveryDate,
    User? customer,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      orderDate: orderDate ?? this.orderDate,
      shippedDate: shippedDate ?? this.shippedDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      customer: customer ?? this.customer,
    );
  }

  /// Get the number of items in the order
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  /// Check if the order is completed (delivered)
  bool get isCompleted => status == OrderStatus.delivered;

  /// Check if the order is cancelled
  bool get isCancelled => status == OrderStatus.cancelled;

  /// Check if the order can be cancelled
  bool get canBeCancelled => status == OrderStatus.pending || status == OrderStatus.processing;

  @override
  List<Object?> get props => [
        id,
        customerId,
        customerName,
        items,
        subtotal,
        tax,
        shipping,
        discount,
        total,
        status,
        notes,
        orderDate,
        shippedDate,
        deliveryDate,
        customer,
      ];
}