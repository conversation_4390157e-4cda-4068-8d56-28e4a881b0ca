/// Permission entity for role-based access control
class Permission {
  final String id;
  final String name;
  final String description;
  final PermissionCategory category;
  final bool isActive;

  const Permission({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    this.isActive = true,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Permission &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Permission categories for better organization
enum PermissionCategory {
  products,
  orders,
  customers,
  inventory,
  reports,
  settings,
  users,
  financial,
}

extension PermissionCategoryExtension on PermissionCategory {
  String get name {
    switch (this) {
      case PermissionCategory.products:
        return 'إدارة المنتجات';
      case PermissionCategory.orders:
        return 'إدارة الطلبات';
      case PermissionCategory.customers:
        return 'إدارة العملاء';
      case PermissionCategory.inventory:
        return 'إدارة المخزون';
      case PermissionCategory.reports:
        return 'التقارير';
      case PermissionCategory.settings:
        return 'الإعدادات';
      case PermissionCategory.users:
        return 'إدارة المستخدمين';
      case PermissionCategory.financial:
        return 'الشؤون المالية';
    }
  }

  String get icon {
    switch (this) {
      case PermissionCategory.products:
        return 'inventory_2';
      case PermissionCategory.orders:
        return 'shopping_cart';
      case PermissionCategory.customers:
        return 'people';
      case PermissionCategory.inventory:
        return 'warehouse';
      case PermissionCategory.reports:
        return 'analytics';
      case PermissionCategory.settings:
        return 'settings';
      case PermissionCategory.users:
        return 'admin_panel_settings';
      case PermissionCategory.financial:
        return 'account_balance';
    }
  }
}

/// Predefined permissions for the system
class SystemPermissions {
  // Products permissions
  static const Permission viewProducts = Permission(
    id: 'view_products',
    name: 'عرض المنتجات',
    description: 'عرض قائمة المنتجات وتفاصيلها',
    category: PermissionCategory.products,
  );

  static const Permission addProducts = Permission(
    id: 'add_products',
    name: 'إضافة المنتجات',
    description: 'إضافة منتجات جديدة للنظام',
    category: PermissionCategory.products,
  );

  static const Permission editProducts = Permission(
    id: 'edit_products',
    name: 'تعديل المنتجات',
    description: 'تعديل معلومات المنتجات الموجودة',
    category: PermissionCategory.products,
  );

  static const Permission deleteProducts = Permission(
    id: 'delete_products',
    name: 'حذف المنتجات',
    description: 'حذف المنتجات من النظام',
    category: PermissionCategory.products,
  );

  static const Permission manageCategories = Permission(
    id: 'manage_categories',
    name: 'إدارة الفئات',
    description: 'إضافة وتعديل وحذف فئات المنتجات',
    category: PermissionCategory.products,
  );

  // Orders permissions
  static const Permission viewOrders = Permission(
    id: 'view_orders',
    name: 'عرض الطلبات',
    description: 'عرض قائمة الطلبات وتفاصيلها',
    category: PermissionCategory.orders,
  );

  static const Permission createOrders = Permission(
    id: 'create_orders',
    name: 'إنشاء الطلبات',
    description: 'إنشاء طلبات جديدة',
    category: PermissionCategory.orders,
  );

  static const Permission editOrders = Permission(
    id: 'edit_orders',
    name: 'تعديل الطلبات',
    description: 'تعديل معلومات الطلبات',
    category: PermissionCategory.orders,
  );

  static const Permission cancelOrders = Permission(
    id: 'cancel_orders',
    name: 'إلغاء الطلبات',
    description: 'إلغاء الطلبات الموجودة',
    category: PermissionCategory.orders,
  );

  static const Permission updateOrderStatus = Permission(
    id: 'update_order_status',
    name: 'تحديث حالة الطلبات',
    description: 'تغيير حالة الطلبات (معلق، قيد المعالجة، إلخ)',
    category: PermissionCategory.orders,
  );

  // Customers permissions
  static const Permission viewCustomers = Permission(
    id: 'view_customers',
    name: 'عرض العملاء',
    description: 'عرض قائمة العملاء ومعلوماتهم',
    category: PermissionCategory.customers,
  );

  static const Permission addCustomers = Permission(
    id: 'add_customers',
    name: 'إضافة العملاء',
    description: 'إضافة عملاء جدد للنظام',
    category: PermissionCategory.customers,
  );

  static const Permission editCustomers = Permission(
    id: 'edit_customers',
    name: 'تعديل العملاء',
    description: 'تعديل معلومات العملاء',
    category: PermissionCategory.customers,
  );

  static const Permission deleteCustomers = Permission(
    id: 'delete_customers',
    name: 'حذف العملاء',
    description: 'حذف العملاء من النظام',
    category: PermissionCategory.customers,
  );

  static const Permission manageCreditLimits = Permission(
    id: 'manage_credit_limits',
    name: 'إدارة حدود الائتمان',
    description: 'تعديل حدود الائتمان للعملاء',
    category: PermissionCategory.customers,
  );

  // Inventory permissions
  static const Permission viewInventory = Permission(
    id: 'view_inventory',
    name: 'عرض المخزون',
    description: 'عرض معلومات المخزون والكميات',
    category: PermissionCategory.inventory,
  );

  static const Permission updateInventory = Permission(
    id: 'update_inventory',
    name: 'تحديث المخزون',
    description: 'تحديث كميات المخزون',
    category: PermissionCategory.inventory,
  );

  static const Permission inventoryAdjustments = Permission(
    id: 'inventory_adjustments',
    name: 'تعديلات المخزون',
    description: 'إجراء تعديلات على المخزون',
    category: PermissionCategory.inventory,
  );

  // Reports permissions
  static const Permission viewReports = Permission(
    id: 'view_reports',
    name: 'عرض التقارير',
    description: 'عرض التقارير والإحصائيات',
    category: PermissionCategory.reports,
  );

  static const Permission exportReports = Permission(
    id: 'export_reports',
    name: 'تصدير التقارير',
    description: 'تصدير التقارير بصيغ مختلفة',
    category: PermissionCategory.reports,
  );

  static const Permission advancedReports = Permission(
    id: 'advanced_reports',
    name: 'التقارير المتقدمة',
    description: 'الوصول للتقارير المتقدمة والتحليلات',
    category: PermissionCategory.reports,
  );

  // Settings permissions
  static const Permission viewSettings = Permission(
    id: 'view_settings',
    name: 'عرض الإعدادات',
    description: 'عرض إعدادات النظام',
    category: PermissionCategory.settings,
  );

  static const Permission editSettings = Permission(
    id: 'edit_settings',
    name: 'تعديل الإعدادات',
    description: 'تعديل إعدادات النظام',
    category: PermissionCategory.settings,
  );

  static const Permission systemBackup = Permission(
    id: 'system_backup',
    name: 'النسخ الاحتياطي',
    description: 'إنشاء واستعادة النسخ الاحتياطية',
    category: PermissionCategory.settings,
  );

  // Users permissions
  static const Permission viewUsers = Permission(
    id: 'view_users',
    name: 'عرض المستخدمين',
    description: 'عرض قائمة المستخدمين ومعلوماتهم',
    category: PermissionCategory.users,
  );

  static const Permission addUsers = Permission(
    id: 'add_users',
    name: 'إضافة المستخدمين',
    description: 'إضافة مستخدمين جدد للنظام',
    category: PermissionCategory.users,
  );

  static const Permission editUsers = Permission(
    id: 'edit_users',
    name: 'تعديل المستخدمين',
    description: 'تعديل معلومات المستخدمين',
    category: PermissionCategory.users,
  );

  static const Permission deleteUsers = Permission(
    id: 'delete_users',
    name: 'حذف المستخدمين',
    description: 'حذف المستخدمين من النظام',
    category: PermissionCategory.users,
  );

  static const Permission manageRoles = Permission(
    id: 'manage_roles',
    name: 'إدارة الأدوار',
    description: 'إنشاء وتعديل أدوار المستخدمين',
    category: PermissionCategory.users,
  );

  // Financial permissions
  static const Permission viewFinancials = Permission(
    id: 'view_financials',
    name: 'عرض الشؤون المالية',
    description: 'عرض المعلومات المالية والحسابات',
    category: PermissionCategory.financial,
  );

  static const Permission managePayments = Permission(
    id: 'manage_payments',
    name: 'إدارة المدفوعات',
    description: 'إدارة المدفوعات والفواتير',
    category: PermissionCategory.financial,
  );

  static const Permission viewProfitLoss = Permission(
    id: 'view_profit_loss',
    name: 'عرض الأرباح والخسائر',
    description: 'عرض تقارير الأرباح والخسائر',
    category: PermissionCategory.financial,
  );

  /// Get all available permissions
  static List<Permission> get allPermissions => [
        // Products
        viewProducts,
        addProducts,
        editProducts,
        deleteProducts,
        manageCategories,
        
        // Orders
        viewOrders,
        createOrders,
        editOrders,
        cancelOrders,
        updateOrderStatus,
        
        // Customers
        viewCustomers,
        addCustomers,
        editCustomers,
        deleteCustomers,
        manageCreditLimits,
        
        // Inventory
        viewInventory,
        updateInventory,
        inventoryAdjustments,
        
        // Reports
        viewReports,
        exportReports,
        advancedReports,
        
        // Settings
        viewSettings,
        editSettings,
        systemBackup,
        
        // Users
        viewUsers,
        addUsers,
        editUsers,
        deleteUsers,
        manageRoles,
        
        // Financial
        viewFinancials,
        managePayments,
        viewProfitLoss,
      ];

  /// Get permissions by category
  static List<Permission> getPermissionsByCategory(PermissionCategory category) {
    return allPermissions.where((p) => p.category == category).toList();
  }
}