import 'package:equatable/equatable.dart';

/// Product entity representing a product in the inventory
class Product extends Equatable {
  final String id;
  final String name;
  final String description;
  final double price;
  final double? discountPrice;
  final int stockQuantity;
  final String categoryId;
  final String? imageUrl;
  final String barcode;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.discountPrice,
    required this.stockQuantity,
    required this.categoryId,
    this.imageUrl,
    required this.barcode,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this product with the given fields replaced
  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    double? discountPrice,
    int? stockQuantity,
    String? categoryId,
    String? imageUrl,
    String? barcode,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      discountPrice: discountPrice ?? this.discountPrice,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      categoryId: categoryId ?? this.categoryId,
      imageUrl: imageUrl ?? this.imageUrl,
      barcode: barcode ?? this.barcode,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if the product is on sale
  bool get isOnSale => discountPrice != null && discountPrice! < price;

  /// Get the current price (discount price if on sale, regular price otherwise)
  double get currentPrice => isOnSale ? discountPrice! : price;

  /// Check if the product is in stock
  bool get isInStock => stockQuantity > 0;

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        discountPrice,
        stockQuantity,
        categoryId,
        imageUrl,
        barcode,
        isActive,
        createdAt,
        updatedAt,
      ];
}