import 'permission.dart';

/// Role entity for role-based access control
class Role {
  final String id;
  final String name;
  final String description;
  final List<Permission> permissions;
  final bool isActive;
  final bool isSystemRole;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Role({
    required this.id,
    required this.name,
    required this.description,
    required this.permissions,
    this.isActive = true,
    this.isSystemRole = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Check if role has a specific permission
  bool hasPermission(String permissionId) {
    return permissions.any((p) => p.id == permissionId);
  }

  /// Check if role has any permission from a list
  bool hasAnyPermission(List<String> permissionIds) {
    return permissionIds.any((id) => hasPermission(id));
  }

  /// Check if role has all permissions from a list
  bool hasAllPermissions(List<String> permissionIds) {
    return permissionIds.every((id) => hasPermission(id));
  }

  /// Get permissions by category
  List<Permission> getPermissionsByCategory(PermissionCategory category) {
    return permissions.where((p) => p.category == category).toList();
  }

  /// Copy role with new permissions
  Role copyWith({
    String? id,
    String? name,
    String? description,
    List<Permission>? permissions,
    bool? isActive,
    bool? isSystemRole,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Role(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      isSystemRole: isSystemRole ?? this.isSystemRole,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Role &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Predefined system roles
class SystemRoles {
  /// Super Admin - Full access to everything
  static final Role superAdmin = Role(
    id: 'super_admin',
    name: 'مدير النظام',
    description: 'صلاحيات كاملة لجميع أجزاء النظام',
    permissions: SystemPermissions.allPermissions,
    isSystemRole: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Owner - Business owner with most permissions
  static final Role owner = Role(
    id: 'owner',
    name: 'المالك',
    description: 'مالك المتجر مع صلاحيات إدارية واسعة',
    permissions: [
      // Products
      SystemPermissions.viewProducts,
      SystemPermissions.addProducts,
      SystemPermissions.editProducts,
      SystemPermissions.deleteProducts,
      SystemPermissions.manageCategories,
      
      // Orders
      SystemPermissions.viewOrders,
      SystemPermissions.createOrders,
      SystemPermissions.editOrders,
      SystemPermissions.cancelOrders,
      SystemPermissions.updateOrderStatus,
      
      // Customers
      SystemPermissions.viewCustomers,
      SystemPermissions.addCustomers,
      SystemPermissions.editCustomers,
      SystemPermissions.deleteCustomers,
      SystemPermissions.manageCreditLimits,
      
      // Inventory
      SystemPermissions.viewInventory,
      SystemPermissions.updateInventory,
      SystemPermissions.inventoryAdjustments,
      
      // Reports
      SystemPermissions.viewReports,
      SystemPermissions.exportReports,
      SystemPermissions.advancedReports,
      
      // Settings
      SystemPermissions.viewSettings,
      SystemPermissions.editSettings,
      SystemPermissions.systemBackup,
      
      // Users
      SystemPermissions.viewUsers,
      SystemPermissions.addUsers,
      SystemPermissions.editUsers,
      SystemPermissions.deleteUsers,
      SystemPermissions.manageRoles,
      
      // Financial
      SystemPermissions.viewFinancials,
      SystemPermissions.managePayments,
      SystemPermissions.viewProfitLoss,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Manager - Store manager with operational permissions
  static final Role manager = Role(
    id: 'manager',
    name: 'المدير',
    description: 'مدير المتجر مع صلاحيات تشغيلية',
    permissions: [
      // Products
      SystemPermissions.viewProducts,
      SystemPermissions.addProducts,
      SystemPermissions.editProducts,
      SystemPermissions.manageCategories,
      
      // Orders
      SystemPermissions.viewOrders,
      SystemPermissions.createOrders,
      SystemPermissions.editOrders,
      SystemPermissions.updateOrderStatus,
      
      // Customers
      SystemPermissions.viewCustomers,
      SystemPermissions.addCustomers,
      SystemPermissions.editCustomers,
      SystemPermissions.manageCreditLimits,
      
      // Inventory
      SystemPermissions.viewInventory,
      SystemPermissions.updateInventory,
      SystemPermissions.inventoryAdjustments,
      
      // Reports
      SystemPermissions.viewReports,
      SystemPermissions.exportReports,
      
      // Settings
      SystemPermissions.viewSettings,
      
      // Users
      SystemPermissions.viewUsers,
      
      // Financial
      SystemPermissions.viewFinancials,
      SystemPermissions.managePayments,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Employee - Regular employee with basic permissions
  static final Role employee = Role(
    id: 'employee',
    name: 'الموظف',
    description: 'موظف عادي مع صلاحيات أساسية',
    permissions: [
      // Products
      SystemPermissions.viewProducts,
      SystemPermissions.addProducts,
      SystemPermissions.editProducts,
      
      // Orders
      SystemPermissions.viewOrders,
      SystemPermissions.createOrders,
      SystemPermissions.updateOrderStatus,
      
      // Customers
      SystemPermissions.viewCustomers,
      SystemPermissions.addCustomers,
      SystemPermissions.editCustomers,
      
      // Inventory
      SystemPermissions.viewInventory,
      SystemPermissions.updateInventory,
      
      // Reports
      SystemPermissions.viewReports,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Sales Representative - Focused on sales activities
  static final Role salesRep = Role(
    id: 'sales_rep',
    name: 'مندوب المبيعات',
    description: 'مندوب مبيعات مع صلاحيات محدودة',
    permissions: [
      // Products
      SystemPermissions.viewProducts,
      
      // Orders
      SystemPermissions.viewOrders,
      SystemPermissions.createOrders,
      
      // Customers
      SystemPermissions.viewCustomers,
      SystemPermissions.addCustomers,
      SystemPermissions.editCustomers,
      
      // Inventory
      SystemPermissions.viewInventory,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Customer - External customer with limited access
  static final Role customer = Role(
    id: 'customer',
    name: 'العميل',
    description: 'عميل خارجي مع صلاحيات محدودة جداً',
    permissions: [
      // Products
      SystemPermissions.viewProducts,
      
      // Orders
      SystemPermissions.viewOrders,
      SystemPermissions.createOrders,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Accountant - Financial focused role
  static final Role accountant = Role(
    id: 'accountant',
    name: 'المحاسب',
    description: 'محاسب مع صلاحيات مالية',
    permissions: [
      // Products
      SystemPermissions.viewProducts,
      
      // Orders
      SystemPermissions.viewOrders,
      
      // Customers
      SystemPermissions.viewCustomers,
      SystemPermissions.manageCreditLimits,
      
      // Inventory
      SystemPermissions.viewInventory,
      
      // Reports
      SystemPermissions.viewReports,
      SystemPermissions.exportReports,
      SystemPermissions.advancedReports,
      
      // Financial
      SystemPermissions.viewFinancials,
      SystemPermissions.managePayments,
      SystemPermissions.viewProfitLoss,
    ],
    isSystemRole: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Get all predefined system roles
  static List<Role> get allSystemRoles => [
        superAdmin,
        owner,
        manager,
        employee,
        salesRep,
        customer,
        accountant,
      ];

  /// Get role by ID
  static Role? getRoleById(String roleId) {
    try {
      return allSystemRoles.firstWhere((role) => role.id == roleId);
    } catch (e) {
      return null;
    }
  }

  /// Get roles suitable for internal users (excluding customer)
  static List<Role> get internalRoles => [
        superAdmin,
        owner,
        manager,
        employee,
        salesRep,
        accountant,
      ];

  /// Get roles suitable for external users
  static List<Role> get externalRoles => [
        customer,
      ];
}

/// Role hierarchy levels for UI organization
enum RoleLevel {
  system,     // System administrator
  owner,      // Business owner
  management, // Managers
  staff,      // Regular staff
  external,   // External users
}

extension RoleLevelExtension on RoleLevel {
  String get name {
    switch (this) {
      case RoleLevel.system:
        return 'مدير النظام';
      case RoleLevel.owner:
        return 'المالك';
      case RoleLevel.management:
        return 'الإدارة';
      case RoleLevel.staff:
        return 'الموظفين';
      case RoleLevel.external:
        return 'المستخدمين الخارجيين';
    }
  }

  int get priority {
    switch (this) {
      case RoleLevel.system:
        return 5;
      case RoleLevel.owner:
        return 4;
      case RoleLevel.management:
        return 3;
      case RoleLevel.staff:
        return 2;
      case RoleLevel.external:
        return 1;
    }
  }
}