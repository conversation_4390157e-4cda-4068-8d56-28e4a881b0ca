import 'package:equatable/equatable.dart';

import 'order.dart';

/// Transaction type enum
enum TransactionType {
  sale,
  refund,
  payment,
  credit,
  debit,
  adjustment,
}

/// Payment method enum
enum PaymentMethod {
  cash,
  creditCard,
  debitCard,
  bankTransfer,
  mobilePayment,
  check,
  storeCredit,
}

/// Transaction entity representing a financial transaction
class Transaction extends Equatable {
  final String id;
  final String userId;
  final String? orderId;
  final TransactionType type;
  final PaymentMethod paymentMethod;
  final double amount;
  final String? referenceNumber;
  final String? notes;
  final bool isSuccess;
  final DateTime transactionDate;
  final Order? order; // For detailed order information

  const Transaction({
    required this.id,
    required this.userId,
    this.orderId,
    required this.type,
    required this.paymentMethod,
    required this.amount,
    this.referenceNumber,
    this.notes,
    required this.isSuccess,
    required this.transactionDate,
    this.order,
  });

  /// Creates a copy of this transaction with the given fields replaced
  Transaction copyWith({
    String? id,
    String? userId,
    String? orderId,
    TransactionType? type,
    PaymentMethod? paymentMethod,
    double? amount,
    String? referenceNumber,
    String? notes,
    bool? isSuccess,
    DateTime? transactionDate,
    Order? order,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      orderId: orderId ?? this.orderId,
      type: type ?? this.type,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      amount: amount ?? this.amount,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      notes: notes ?? this.notes,
      isSuccess: isSuccess ?? this.isSuccess,
      transactionDate: transactionDate ?? this.transactionDate,
      order: order ?? this.order,
    );
  }

  /// Check if the transaction is related to an order
  bool get isOrderRelated => orderId != null;

  /// Check if the transaction is a payment
  bool get isPayment => type == TransactionType.payment;

  /// Check if the transaction is a refund
  bool get isRefund => type == TransactionType.refund;

  /// Check if the transaction increases balance (positive)
  bool get isPositive => type == TransactionType.sale || 
                        type == TransactionType.payment || 
                        type == TransactionType.credit;

  /// Check if the transaction decreases balance (negative)
  bool get isNegative => type == TransactionType.refund || 
                        type == TransactionType.debit;

  @override
  List<Object?> get props => [
        id,
        userId,
        orderId,
        type,
        paymentMethod,
        amount,
        referenceNumber,
        notes,
        isSuccess,
        transactionDate,
        order,
      ];
}