import 'package:equatable/equatable.dart';

/// User roles in the application
enum UserRole {
  owner,
  employee,
  customer,
  wholesaler,
  agent,
  admin,
}

/// User entity representing a user of the application
class User extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? phoneNumber;
  final UserRole role;
  final String? imageUrl;
  final bool isActive;
  final DateTime createdAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    required this.role,
    this.imageUrl,
    required this.isActive,
    required this.createdAt,
  });

  /// Check if the user has owner privileges
  bool get isOwner => role == UserRole.owner;

  /// Check if the user has admin privileges
  bool get isAdmin => role == UserRole.admin;

  /// Check if the user has employee privileges
  bool get isEmployee => role == UserRole.employee;

  /// Check if the user has customer privileges
  bool get isCustomer => role == UserRole.customer;

  /// Check if the user has wholesaler privileges
  bool get isWholesaler => role == UserRole.wholesaler;

  /// Check if the user has agent privileges
  bool get isAgent => role == UserRole.agent;

  /// Creates a copy of this user with the given fields replaced
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phoneNumber,
    UserRole? role,
    String? imageUrl,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phoneNumber,
        role,
        imageUrl,
        isActive,
        createdAt,
      ];
}