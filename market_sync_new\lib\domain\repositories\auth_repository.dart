import 'package:dartz/dartz.dart';

import '../entities/user.dart';
import '../../core/error/failures.dart';

/// Repository interface for authentication operations
abstract class AuthRepository {
  /// Check if a user is currently authenticated
  Future<Either<Failure, User>> checkAuthStatus();

  /// Log in a user with email and password
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  });

  /// Log out the current user
  Future<Either<Failure, void>> logout();

  /// Register a new user
  Future<Either<Failure, User>> register({
    required String name,
    required String email,
    required String password,
    String? phoneNumber,
  });

  /// Update the user's profile
  Future<Either<Failure, User>> updateProfile({
    required String name,
    String? phoneNumber,
    String? imageUrl,
  });
}