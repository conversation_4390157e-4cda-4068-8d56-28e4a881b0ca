import 'package:dartz/dartz.dart';

import '../entities/user.dart';
import '../../core/error/failures.dart';

/// Repository interface for customer operations
abstract class CustomerRepository {
  /// Get a list of all customers
  Future<Either<Failure, List<User>>> getCustomers();
  
  /// Get a customer by ID
  Future<Either<Failure, User>> getCustomerById(String id);
  
  /// Create a new customer
  Future<Either<Failure, User>> createCustomer(User customer);
  
  /// Update an existing customer
  Future<Either<Failure, User>> updateCustomer(User customer);
  
  /// Delete a customer by ID
  Future<Either<Failure, bool>> deleteCustomer(String id);
  
  /// Search customers by query
  Future<Either<Failure, List<User>>> searchCustomers(String query);
  
  /// Get a customer's order history
  Future<Either<Failure, List<dynamic>>> getCustomerOrderHistory(String customerId);
  
  /// Get customers with outstanding balances
  Future<Either<Failure, List<User>>> getCustomersWithOutstandingBalances();
  
  /// Update a customer's credit limit
  Future<Either<Failure, bool>> updateCustomerCreditLimit(String customerId, double creditLimit);
  
  /// Get a customer's credit balance
  Future<Either<Failure, double>> getCustomerCreditBalance(String customerId);
  
  /// Get customer statistics
  Future<Either<Failure, Map<String, dynamic>>> getCustomerStatistics(String customerId);
  
  /// Add credit to a customer account
  Future<Either<Failure, bool>> addCustomerCredit(
    String customerId,
    double amount,
    String reason,
  );
}