import 'package:dartz/dartz.dart';

import '../entities/order.dart' as entity;
import '../../core/error/failures.dart';

/// Repository interface for order operations
abstract class OrderRepository {
  /// Get a list of all orders
  Future<Either<Failure, List<entity.Order>>> getOrders();
  
  /// Get an order by its ID
  Future<Either<Failure, entity.Order>> getOrderById(String id);
  
  /// Get orders by customer ID
  Future<Either<Failure, List<entity.Order>>> getOrdersByCustomerId(String customerId);
  
  /// Create a new order
  Future<Either<Failure, entity.Order>> createOrder(entity.Order order);
  
  /// Update an existing order
  Future<Either<Failure, entity.Order>> updateOrder(entity.Order order);
  
  /// Delete an order by its ID
  Future<Either<Failure, bool>> deleteOrder(String id);
  
  /// Update order status
  Future<Either<Failure, entity.Order>> updateOrderStatus(String id, entity.OrderStatus status);
  
  /// Get orders by status
  Future<Either<Failure, List<entity.Order>>> getOrdersByStatus(entity.OrderStatus status);
  
  /// Get orders by date range
  Future<Either<Failure, List<entity.Order>>> getOrdersByDateRange(
    DateTime startDate, 
    DateTime endDate,
  );
  
  /// Search orders by query
  Future<Either<Failure, List<entity.Order>>> searchOrders(String query);
  
  /// Process order payment
  Future<Either<Failure, bool>> processOrderPayment(
    String orderId, 
    double amount, 
    String paymentMethod,
  );
  
  /// Cancel an order
  Future<Either<Failure, bool>> cancelOrder(String id, String reason);
}