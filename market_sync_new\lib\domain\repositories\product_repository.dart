import 'package:dartz/dartz.dart';

import '../entities/product.dart';
import '../entities/category.dart';
import '../../core/error/failures.dart';

/// Repository interface for product operations
abstract class ProductRepository {
  /// Get a list of all products
  Future<Either<Failure, List<Product>>> getProducts();
  
  /// Get a product by its ID
  Future<Either<Failure, Product>> getProductById(String id);
  
  /// Get products by category ID
  Future<Either<Failure, List<Product>>> getProductsByCategoryId(String categoryId);
  
  /// Create a new product
  Future<Either<Failure, Product>> createProduct(Product product);
  
  /// Update an existing product
  Future<Either<Failure, Product>> updateProduct(Product product);
  
  /// Delete a product by its ID
  Future<Either<Failure, bool>> deleteProduct(String id);
  
  /// Search products by query
  Future<Either<Failure, List<Product>>> searchProducts(String query);
  
  /// Get products with low stock
  Future<Either<Failure, List<Product>>> getLowStockProducts(int threshold);
  
  /// Get a list of all categories
  Future<Either<Failure, List<Category>>> getCategories();
  
  /// Get a category by its ID
  Future<Either<Failure, Category>> getCategoryById(String id);
  
  /// Create a new category
  Future<Either<Failure, Category>> createCategory(Category category);
  
  /// Update an existing category
  Future<Either<Failure, Category>> updateCategory(Category category);
  
  /// Delete a category by its ID
  Future<Either<Failure, bool>> deleteCategory(String id);
}