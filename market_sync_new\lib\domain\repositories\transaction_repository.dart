import 'package:dartz/dartz.dart';

import '../entities/transaction.dart';
import '../../core/error/failures.dart';

/// Repository interface for transaction operations
abstract class TransactionRepository {
  /// Get a list of all transactions
  Future<Either<Failure, List<Transaction>>> getTransactions();
  
  /// Get a transaction by ID
  Future<Either<Failure, Transaction>> getTransactionById(String id);
  
  /// Get transactions by user ID
  Future<Either<Failure, List<Transaction>>> getTransactionsByUserId(String userId);
  
  /// Get transactions by order ID
  Future<Either<Failure, List<Transaction>>> getTransactionsByOrderId(String orderId);
  
  /// Create a new transaction
  Future<Either<Failure, Transaction>> createTransaction(Transaction transaction);
  
  /// Update an existing transaction
  Future<Either<Failure, Transaction>> updateTransaction(Transaction transaction);
  
  /// Get transactions by date range
  Future<Either<Failure, List<Transaction>>> getTransactionsByDateRange(
    DateTime startDate, 
    DateTime endDate,
  );
  
  /// Get transactions by type
  Future<Either<Failure, List<Transaction>>> getTransactionsByType(
    TransactionType type,
  );
  
  /// Get transactions by payment method
  Future<Either<Failure, List<Transaction>>> getTransactionsByPaymentMethod(
    PaymentMethod paymentMethod,
  );
  
  /// Get daily transaction summary
  Future<Either<Failure, Map<String, dynamic>>> getDailyTransactionSummary(DateTime date);
  
  /// Get monthly transaction summary
  Future<Either<Failure, Map<String, dynamic>>> getMonthlyTransactionSummary(
    int month, 
    int year,
  );
  
  /// Get transaction statistics
  Future<Either<Failure, Map<String, dynamic>>> getTransactionStatistics();
}