import 'package:dartz/dartz.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/user.dart';
import '../../repositories/auth_repository.dart';

/// Use case for checking authentication status
class CheckAuthStatusUseCase implements NoParamsUseCase<User> {
  final AuthRepository repository;

  CheckAuthStatusUseCase({required this.repository});

  @override
  Future<Either<Failure, User>> call() async {
    return await repository.checkAuthStatus();
  }
}