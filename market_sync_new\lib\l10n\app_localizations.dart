import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static final Map<String, Map<String, String>> _localizedValues = {
    'en': {
      'appTitle': 'Market Sync',
      'login': 'Login',
      'register': 'Register',
      'email': 'Email',
      'password': 'Password',
      'dashboard': 'Dashboard',
      'inventory': 'Inventory',
      'orders': 'Orders',
      'customers': 'Customers',
      'products': 'Products',
      'settings': 'Settings',
      'profile': 'Profile',
      'logout': 'Logout',
      'search': 'Search',
      'notifications': 'Notifications',
      'reports': 'Reports',
      'analytics': 'Analytics',
      'sales': 'Sales',
      'transactions': 'Transactions',
      'suppliers': 'Suppliers',
      'employees': 'Employees',
      'categories': 'Categories',
      'addProduct': 'Add Product',
      'editProduct': 'Edit Product',
      'deleteProduct': 'Delete Product',
      'save': 'Save',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'error': 'Error',
      'success': 'Success',
      'loading': 'Loading...',
    },
    'ar': {
      'appTitle': 'ماركت سينك',
      'login': 'تسجيل الدخول',
      'register': 'تسجيل',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'dashboard': 'لوحة التحكم',
      'inventory': 'المخزون',
      'orders': 'الطلبات',
      'customers': 'العملاء',
      'products': 'المنتجات',
      'settings': 'الإعدادات',
      'profile': 'الملف الشخصي',
      'logout': 'تسجيل الخروج',
      'search': 'بحث',
      'notifications': 'الإشعارات',
      'reports': 'التقارير',
      'analytics': 'التحليلات',
      'sales': 'المبيعات',
      'transactions': 'المعاملات',
      'suppliers': 'الموردين',
      'employees': 'الموظفين',
      'categories': 'الفئات',
      'addProduct': 'إضافة منتج',
      'editProduct': 'تعديل المنتج',
      'deleteProduct': 'حذف المنتج',
      'save': 'حفظ',
      'cancel': 'إلغاء',
      'confirm': 'تأكيد',
      'error': 'خطأ',
      'success': 'نجاح',
      'loading': 'جاري التحميل...',
    },
  };

  String get appTitle => _localizedValues[locale.languageCode]!['appTitle']!;
  String get login => _localizedValues[locale.languageCode]!['login']!;
  String get register => _localizedValues[locale.languageCode]!['register']!;
  String get email => _localizedValues[locale.languageCode]!['email']!;
  String get password => _localizedValues[locale.languageCode]!['password']!;
  String get dashboard => _localizedValues[locale.languageCode]!['dashboard']!;
  String get inventory => _localizedValues[locale.languageCode]!['inventory']!;
  String get orders => _localizedValues[locale.languageCode]!['orders']!;
  String get customers => _localizedValues[locale.languageCode]!['customers']!;
  String get products => _localizedValues[locale.languageCode]!['products']!;
  String get settings => _localizedValues[locale.languageCode]!['settings']!;
  String get profile => _localizedValues[locale.languageCode]!['profile']!;
  String get logout => _localizedValues[locale.languageCode]!['logout']!;
  String get search => _localizedValues[locale.languageCode]!['search']!;
  String get notifications => _localizedValues[locale.languageCode]!['notifications']!;
  String get reports => _localizedValues[locale.languageCode]!['reports']!;
  String get analytics => _localizedValues[locale.languageCode]!['analytics']!;
  String get sales => _localizedValues[locale.languageCode]!['sales']!;
  String get transactions => _localizedValues[locale.languageCode]!['transactions']!;
  String get suppliers => _localizedValues[locale.languageCode]!['suppliers']!;
  String get employees => _localizedValues[locale.languageCode]!['employees']!;
  String get categories => _localizedValues[locale.languageCode]!['categories']!;
  String get addProduct => _localizedValues[locale.languageCode]!['addProduct']!;
  String get editProduct => _localizedValues[locale.languageCode]!['editProduct']!;
  String get deleteProduct => _localizedValues[locale.languageCode]!['deleteProduct']!;
  String get save => _localizedValues[locale.languageCode]!['save']!;
  String get cancel => _localizedValues[locale.languageCode]!['cancel']!;
  String get confirm => _localizedValues[locale.languageCode]!['confirm']!;
  String get error => _localizedValues[locale.languageCode]!['error']!;
  String get success => _localizedValues[locale.languageCode]!['success']!;
  String get loading => _localizedValues[locale.languageCode]!['loading']!;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}