import 'package:equatable/equatable.dart';

/// Base class for all authentication events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to check the current authentication status
class CheckAuthStatusEvent extends AuthEvent {
  const CheckAuthStatusEvent();
}

/// Event to log a user in
class LoginEvent extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;

  const LoginEvent({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [email, password, rememberMe];
}

/// Event to log a user out
class LogoutEvent extends AuthEvent {
  const LogoutEvent();
}

/// Event to register a new user
class RegisterEvent extends AuthEvent {
  final String name;
  final String email;
  final String password;
  final String? phoneNumber;

  const RegisterEvent({
    required this.name,
    required this.email,
    required this.password,
    this.phoneNumber,
  });

  @override
  List<Object?> get props => [name, email, password, phoneNumber];
}

/// Event to update the user's profile
class UpdateProfileEvent extends AuthEvent {
  final String name;
  final String? phoneNumber;
  final String? imageUrl;

  const UpdateProfileEvent({
    required this.name,
    this.phoneNumber,
    this.imageUrl,
  });

  @override
  List<Object?> get props => [name, phoneNumber, imageUrl];
}