import 'package:equatable/equatable.dart';

import '../../../domain/entities/user.dart';

/// The authentication states
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Represents the state of the authentication bloc
class AuthState extends Equatable {
  final AuthStatus status;
  final User? user;
  final String? errorMessage;

  const AuthState({
    required this.status,
    this.user,
    this.errorMessage,
  });

  /// Initial state before any authentication checks
  factory AuthState.initial() => const AuthState(status: AuthStatus.initial);

  /// Loading state during authentication processes
  factory AuthState.loading() => const AuthState(status: AuthStatus.loading);

  /// Authenticated state with user data
  factory AuthState.authenticated(User user) => AuthState(
        status: AuthStatus.authenticated,
        user: user,
      );

  /// Unauthenticated state
  factory AuthState.unauthenticated() => const AuthState(status: AuthStatus.unauthenticated);

  /// Error state with error message
  factory AuthState.error(String message) => AuthState(
        status: AuthStatus.error,
        errorMessage: message,
      );

  /// Creates a copy of this state with the given fields replaced
  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, user, errorMessage];
}