import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/entities/user.dart';
import '../../../domain/repositories/customer_repository.dart';
import 'customer_event.dart';
import 'customer_state.dart';

/// Bloc for managing customer state
class CustomerBloc extends Bloc<CustomerEvent, CustomerState> {
  final CustomerRepository customerRepository;
  
  CustomerBloc({required this.customerRepository}) : super(const CustomerState()) {
    on<FetchCustomers>(_onFetchCustomers);
    on<FetchMoreCustomers>(_onFetchMoreCustomers);
    on<FetchCustomerById>(_onFetchCustomerById);
    on<CreateCustomer>(_onCreateCustomer);
    on<UpdateCustomer>(_onUpdateCustomer);
    on<DeleteCustomer>(_onDeleteCustomer);
    on<SearchCustomers>(_onSearchCustomers);
    on<ClearCustomerSearch>(_onClearCustomerSearch);
    on<FilterCustomersByRole>(_onFilterCustomersByRole);
    on<ClearCustomerFilters>(_onClearCustomerFilters);
    on<RefreshCustomers>(_onRefreshCustomers);
    on<SelectCustomer>(_onSelectCustomer);
    on<ClearSelectedCustomer>(_onClearSelectedCustomer);
    on<UpdateCustomerCreditLimit>(_onUpdateCustomerCreditLimit);
    on<AddCustomerCredit>(_onAddCustomerCredit);
  }
  
  /// Handle FetchCustomers event
  Future<void> _onFetchCustomers(
    FetchCustomers event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.loading));
    
    final result = await customerRepository.getCustomers();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customers) => emit(state.copyWith(
        status: CustomerBlocStatus.loaded,
        customers: customers,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle FetchMoreCustomers event
  Future<void> _onFetchMoreCustomers(
    FetchMoreCustomers event,
    Emitter<CustomerState> emit,
  ) async {
    if (!state.hasNextPage) return;
    
    // Logic for pagination would be implemented here
    // For now we'll just fetch all customers again
    final result = await customerRepository.getCustomers();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customers) => emit(state.copyWith(
        customers: [...state.customers, ...customers],
        hasNextPage: customers.isNotEmpty,
      )),
    );
  }
  
  /// Handle FetchCustomerById event
  Future<void> _onFetchCustomerById(
    FetchCustomerById event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.loading));
    
    final result = await customerRepository.getCustomerById(event.customerId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customer) => emit(state.copyWith(
        status: CustomerBlocStatus.loaded,
        selectedCustomer: customer,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle CreateCustomer event
  Future<void> _onCreateCustomer(
    CreateCustomer event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.addingCustomer));
    
    final result = await customerRepository.createCustomer(event.customer);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customer) {
        final updatedCustomers = [...state.customers, customer];
        emit(state.copyWith(
          status: CustomerBlocStatus.customerAdded,
          customers: updatedCustomers,
          selectedCustomer: customer,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle UpdateCustomer event
  Future<void> _onUpdateCustomer(
    UpdateCustomer event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.updatingCustomer));
    
    final result = await customerRepository.updateCustomer(event.customer);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (updatedCustomer) {
        final updatedCustomers = state.customers.map((customer) {
          return customer.id == updatedCustomer.id ? updatedCustomer : customer;
        }).toList();
        
        emit(state.copyWith(
          status: CustomerBlocStatus.customerUpdated,
          customers: updatedCustomers,
          selectedCustomer: updatedCustomer,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle DeleteCustomer event
  Future<void> _onDeleteCustomer(
    DeleteCustomer event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.deletingCustomer));
    
    final result = await customerRepository.deleteCustomer(event.customerId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (success) {
        if (success) {
          final updatedCustomers = state.customers
              .where((customer) => customer.id != event.customerId)
              .toList();
          
          emit(state.copyWith(
            status: CustomerBlocStatus.customerDeleted,
            customers: updatedCustomers,
            selectedCustomer: state.selectedCustomer?.id == event.customerId
                ? null
                : state.selectedCustomer,
            errorMessage: null,
          ));
        } else {
          emit(state.copyWith(
            status: CustomerBlocStatus.error,
            errorMessage: 'Failed to delete customer',
          ));
        }
      },
    );
  }
  
  /// Handle SearchCustomers event
  Future<void> _onSearchCustomers(
    SearchCustomers event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(
      status: CustomerBlocStatus.loading,
      isSearching: true,
      searchQuery: event.query,
    ));
    
    final result = await customerRepository.searchCustomers(event.query);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customers) => emit(state.copyWith(
        status: CustomerBlocStatus.loaded,
        customers: customers,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle ClearCustomerSearch event
  Future<void> _onClearCustomerSearch(
    ClearCustomerSearch event,
    Emitter<CustomerState> emit,
  ) async {
    if (!state.isSearching) return;
    
    emit(state.copyWith(
      status: CustomerBlocStatus.loading,
      isSearching: false,
      searchQuery: '',
    ));
    
    _fetchCustomersWithCurrentFilters(emit);
  }
  
  /// Handle FilterCustomersByRole event
  Future<void> _onFilterCustomersByRole(
    FilterCustomersByRole event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(
      status: CustomerBlocStatus.loading,
      isFiltering: true,
      filterRole: event.role,
    ));
    
    // This is a simplified implementation
    // In a real app, you'd filter by role on the server
    final result = await customerRepository.getCustomers();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customers) {
        final filteredCustomers = customers.where((customer) {
          final roleStr = customer.role.toString().split('.').last;
          return roleStr == event.role;
        }).toList();
        
        emit(state.copyWith(
          status: CustomerBlocStatus.loaded,
          customers: filteredCustomers,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle ClearCustomerFilters event
  Future<void> _onClearCustomerFilters(
    ClearCustomerFilters event,
    Emitter<CustomerState> emit,
  ) async {
    if (!state.isFiltering) return;
    
    emit(state.copyWith(
      status: CustomerBlocStatus.loading,
      isFiltering: false,
      filterRole: null,
    ));
    
    final result = await customerRepository.getCustomers();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customers) => emit(state.copyWith(
        status: CustomerBlocStatus.loaded,
        customers: customers,
        errorMessage: null,
      )),
    );
  }
  
  /// Helper to fetch customers with current filters applied
  Future<void> _fetchCustomersWithCurrentFilters(Emitter<CustomerState> emit) async {
    if (state.isSearching && state.searchQuery.isNotEmpty) {
      final result = await customerRepository.searchCustomers(state.searchQuery);
      _handleCustomerResult(result, emit);
    } else if (state.isFiltering && state.filterRole != null) {
      final result = await customerRepository.getCustomers();
      result.fold(
        (failure) => emit(state.copyWith(
          status: CustomerBlocStatus.error,
          errorMessage: failure.message,
        )),
        (customers) {
          final filteredCustomers = customers.where((customer) {
            final roleStr = customer.role.toString().split('.').last;
            return roleStr == state.filterRole;
          }).toList();
          
          emit(state.copyWith(
            status: CustomerBlocStatus.loaded,
            customers: filteredCustomers,
            errorMessage: null,
          ));
        },
      );
    } else {
      final result = await customerRepository.getCustomers();
      _handleCustomerResult(result, emit);
    }
  }
  
  /// Helper to handle customer result and emit appropriate state
  void _handleCustomerResult(
    dynamic result,
    Emitter<CustomerState> emit,
  ) {
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (customers) => emit(state.copyWith(
        status: CustomerBlocStatus.loaded,
        customers: customers,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle RefreshCustomers event
  Future<void> _onRefreshCustomers(
    RefreshCustomers event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.loading));
    
    _fetchCustomersWithCurrentFilters(emit);
  }
  
  /// Handle SelectCustomer event
  void _onSelectCustomer(
    SelectCustomer event,
    Emitter<CustomerState> emit,
  ) {
    emit(state.copyWith(selectedCustomer: event.customer));
  }
  
  /// Handle ClearSelectedCustomer event
  void _onClearSelectedCustomer(
    ClearSelectedCustomer event,
    Emitter<CustomerState> emit,
  ) {
    emit(state.copyWith(selectedCustomer: null));
  }
  
  /// Handle UpdateCustomerCreditLimit event
  Future<void> _onUpdateCustomerCreditLimit(
    UpdateCustomerCreditLimit event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.updatingCustomer));
    
    final result = await customerRepository.updateCustomerCreditLimit(
      event.customerId,
      event.creditLimit,
    );
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (success) {
        if (success) {
          // If successful, fetch the updated customer
          customerRepository.getCustomerById(event.customerId).then((result) {
            result.fold(
              (failure) => null,
              (updatedCustomer) {
                if (state.selectedCustomer?.id == updatedCustomer.id) {
                  add(SelectCustomer(updatedCustomer));
                }
                
                final updatedCustomers = state.customers.map((customer) {
                  return customer.id == updatedCustomer.id
                      ? updatedCustomer
                      : customer;
                }).toList();
                
                emit(state.copyWith(
                  customers: updatedCustomers,
                ));
              },
            );
          });
          
          emit(state.copyWith(
            status: CustomerBlocStatus.customerUpdated,
            errorMessage: null,
          ));
        } else {
          emit(state.copyWith(
            status: CustomerBlocStatus.error,
            errorMessage: 'Failed to update credit limit',
          ));
        }
      },
    );
  }
  
  /// Handle AddCustomerCredit event
  Future<void> _onAddCustomerCredit(
    AddCustomerCredit event,
    Emitter<CustomerState> emit,
  ) async {
    emit(state.copyWith(status: CustomerBlocStatus.updatingCustomer));
    
    final result = await customerRepository.addCustomerCredit(
      event.customerId,
      event.amount,
      event.reason,
    );
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: CustomerBlocStatus.error,
        errorMessage: failure.message,
      )),
      (success) {
        if (success) {
          // If successful, fetch the updated customer
          customerRepository.getCustomerById(event.customerId).then((result) {
            result.fold(
              (failure) => null,
              (updatedCustomer) {
                if (state.selectedCustomer?.id == updatedCustomer.id) {
                  add(SelectCustomer(updatedCustomer));
                }
                
                final updatedCustomers = state.customers.map((customer) {
                  return customer.id == updatedCustomer.id
                      ? updatedCustomer
                      : customer;
                }).toList();
                
                emit(state.copyWith(
                  customers: updatedCustomers,
                ));
              },
            );
          });
          
          emit(state.copyWith(
            status: CustomerBlocStatus.customerUpdated,
            errorMessage: null,
          ));
        } else {
          emit(state.copyWith(
            status: CustomerBlocStatus.error,
            errorMessage: 'Failed to add credit',
          ));
        }
      },
    );
  }
}