import 'package:equatable/equatable.dart';

import '../../../domain/entities/user.dart';

/// Base class for customer events
abstract class CustomerEvent extends Equatable {
  const CustomerEvent();

  @override
  List<Object?> get props => [];
}

/// Event to fetch customers
class FetchCustomers extends CustomerEvent {
  const FetchCustomers();
}

/// Event to fetch more customers (pagination)
class FetchMoreCustomers extends CustomerEvent {
  const FetchMoreCustomers();
}

/// Event to fetch a single customer by ID
class FetchCustomerById extends CustomerEvent {
  final String customerId;
  
  const FetchCustomerById(this.customerId);
  
  @override
  List<Object> get props => [customerId];
}

/// Event to create a new customer
class CreateCustomer extends CustomerEvent {
  final User customer;
  
  const CreateCustomer(this.customer);
  
  @override
  List<Object> get props => [customer];
}

/// Event to update an existing customer
class UpdateCustomer extends CustomerEvent {
  final User customer;
  
  const UpdateCustomer(this.customer);
  
  @override
  List<Object> get props => [customer];
}

/// Event to delete a customer
class DeleteCustomer extends CustomerEvent {
  final String customerId;
  
  const DeleteCustomer(this.customerId);
  
  @override
  List<Object> get props => [customerId];
}

/// Event to search for customers
class SearchCustomers extends CustomerEvent {
  final String query;
  
  const SearchCustomers(this.query);
  
  @override
  List<Object> get props => [query];
}

/// Event to clear search
class ClearCustomerSearch extends CustomerEvent {
  const ClearCustomerSearch();
}

/// Event to filter customers by role
class FilterCustomersByRole extends CustomerEvent {
  final String role;
  
  const FilterCustomersByRole(this.role);
  
  @override
  List<Object> get props => [role];
}

/// Event to clear filter
class ClearCustomerFilters extends CustomerEvent {
  const ClearCustomerFilters();
}

/// Event to refresh customer data
class RefreshCustomers extends CustomerEvent {
  const RefreshCustomers();
}

/// Event to select a customer
class SelectCustomer extends CustomerEvent {
  final User customer;
  
  const SelectCustomer(this.customer);
  
  @override
  List<Object> get props => [customer];
}

/// Event to clear selected customer
class ClearSelectedCustomer extends CustomerEvent {
  const ClearSelectedCustomer();
}

/// Event to update a customer's credit limit
class UpdateCustomerCreditLimit extends CustomerEvent {
  final String customerId;
  final double creditLimit;
  
  const UpdateCustomerCreditLimit(this.customerId, this.creditLimit);
  
  @override
  List<Object> get props => [customerId, creditLimit];
}

/// Event to add credit to a customer account
class AddCustomerCredit extends CustomerEvent {
  final String customerId;
  final double amount;
  final String reason;
  
  const AddCustomerCredit(this.customerId, this.amount, this.reason);
  
  @override
  List<Object> get props => [customerId, amount, reason];
}