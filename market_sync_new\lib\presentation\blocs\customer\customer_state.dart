import 'package:equatable/equatable.dart';

import '../../../domain/entities/user.dart';

/// Customer states
enum CustomerBlocStatus {
  initial,
  loading,
  loaded,
  error,
  addingCustomer,
  customerAdded,
  updatingCustomer,
  customerUpdated,
  deletingCustomer,
  customerDeleted,
}

/// State for the customer bloc
class CustomerState extends Equatable {
  final CustomerBlocStatus status;
  final List<User> customers;
  final User? selectedCustomer;
  final String? errorMessage;
  final bool hasNextPage;
  final bool isSearching;
  final String searchQuery;
  final bool isFiltering;
  final String? filterRole;
  
  const CustomerState({
    this.status = CustomerBlocStatus.initial,
    this.customers = const [],
    this.selectedCustomer,
    this.errorMessage,
    this.hasNextPage = false,
    this.isSearching = false,
    this.searchQuery = '',
    this.isFiltering = false,
    this.filterRole,
  });
  
  /// Creates a copy of this state with the given fields replaced
  CustomerState copyWith({
    CustomerBlocStatus? status,
    List<User>? customers,
    User? selectedCustomer,
    String? errorMessage,
    bool? hasNextPage,
    bool? isSearching,
    String? searchQuery,
    bool? isFiltering,
    String? filterRole,
  }) {
    return CustomerState(
      status: status ?? this.status,
      customers: customers ?? this.customers,
      selectedCustomer: selectedCustomer ?? this.selectedCustomer,
      errorMessage: errorMessage,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
      isFiltering: isFiltering ?? this.isFiltering,
      filterRole: filterRole ?? this.filterRole,
    );
  }
  
  @override
  List<Object?> get props => [
    status,
    customers,
    selectedCustomer,
    errorMessage,
    hasNextPage,
    isSearching,
    searchQuery,
    isFiltering,
    filterRole,
  ];
}