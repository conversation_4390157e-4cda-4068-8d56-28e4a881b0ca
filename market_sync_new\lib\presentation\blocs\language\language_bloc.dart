import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'language_event.dart';
import 'language_state.dart';

/// Bloc that manages locale/language settings
class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  final SharedPreferences sharedPreferences;
  final String localeKey;
  final Locale defaultLocale;

  LanguageBloc({
    required this.sharedPreferences,
    required this.localeKey,
    required this.defaultLocale,
  }) : super(LanguageState.initial()) {
    on<LoadLanguageEvent>(_onLoadLanguage);
    on<ChangeLanguageEvent>(_onChangeLanguage);
    on<ToggleLanguageEvent>(_onToggleLanguage);
  }

  /// Load the saved locale from shared preferences
  void _onLoadLanguage(LoadLanguageEvent event, Emitter<LanguageState> emit) {
    final String? localeCode = sharedPreferences.getString(localeKey);
    
    if (localeCode != null) {
      emit(state.copyWith(locale: Locale(localeCode)));
    } else {
      emit(state.copyWith(locale: defaultLocale));
    }
  }

  /// Change the locale and save it to shared preferences
  void _onChangeLanguage(ChangeLanguageEvent event, Emitter<LanguageState> emit) async {
    if (!_isSupportedLocale(event.locale)) return;
    
    await sharedPreferences.setString(localeKey, event.locale.languageCode);
    emit(state.copyWith(locale: event.locale));
  }

  /// Toggle between Arabic and English
  void _onToggleLanguage(ToggleLanguageEvent event, Emitter<LanguageState> emit) async {
    final String currentLocaleCode = state.locale.languageCode;
    final String newLocaleCode = currentLocaleCode == 'ar' ? 'en' : 'ar';
    
    await sharedPreferences.setString(localeKey, newLocaleCode);
    emit(state.copyWith(locale: Locale(newLocaleCode)));
  }

  /// Check if the locale is supported
  bool _isSupportedLocale(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }
}