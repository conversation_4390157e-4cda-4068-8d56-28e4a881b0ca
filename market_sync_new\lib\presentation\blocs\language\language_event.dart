import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Base class for all language events
abstract class LanguageEvent extends Equatable {
  const LanguageEvent();

  @override
  List<Object?> get props => [];
}

/// Event to change the app locale
class ChangeLanguageEvent extends LanguageEvent {
  final Locale locale;

  const ChangeLanguageEvent(this.locale);

  @override
  List<Object?> get props => [locale];
}

/// Event to toggle between Arabic and English languages
class ToggleLanguageEvent extends LanguageEvent {
  const ToggleLanguageEvent();
}

/// Event to load the saved locale
class LoadLanguageEvent extends LanguageEvent {
  const LoadLanguageEvent();
}