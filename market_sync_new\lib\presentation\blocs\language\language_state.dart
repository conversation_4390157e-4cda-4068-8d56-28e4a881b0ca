import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Represents the state of the language bloc
class LanguageState extends Equatable {
  final Locale locale;

  const LanguageState({required this.locale});

  /// Initial state with the default locale
  factory LanguageState.initial() => const LanguageState(locale: Locale('ar'));

  /// Creates a copy of this state with the given fields replaced
  LanguageState copyWith({Locale? locale}) {
    return LanguageState(
      locale: locale ?? this.locale,
    );
  }

  @override
  List<Object?> get props => [locale];
}