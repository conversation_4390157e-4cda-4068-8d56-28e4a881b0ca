import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/entities/order.dart' hide OrderStatus;
import '../../../domain/repositories/order_repository.dart';
import 'order_event.dart';
import 'order_state.dart';

// Import with alias to resolve naming conflicts
import 'order_state.dart' show OrderBlocStatus;

/// Bloc for managing order state
class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final OrderRepository orderRepository;
  
  OrderBloc({required this.orderRepository}) : super(const OrderState()) {
    on<FetchOrders>(_onFetchOrders);
    on<FetchMoreOrders>(_onFetchMoreOrders);
    on<FetchOrderById>(_onFetchOrderById);
    on<CreateOrder>(_onCreateOrder);
    on<UpdateOrder>(_onUpdateOrder);
    on<UpdateOrderStatus>(_onUpdateOrderStatus);
    on<DeleteOrder>(_onDeleteOrder);
    on<SearchOrders>(_onSearchOrders);
    on<ClearOrderSearch>(_onClearOrderSearch);
    on<FilterOrdersByStatus>(_onFilterOrdersByStatus);
    on<FilterOrdersByCustomer>(_onFilterOrdersByCustomer);
    on<FilterOrdersByDateRange>(_onFilterOrdersByDateRange);
    on<ClearOrderFilters>(_onClearOrderFilters);
    on<RefreshOrders>(_onRefreshOrders);
    on<SelectOrder>(_onSelectOrder);
    on<ClearSelectedOrder>(_onClearSelectedOrder);
  }
  
  /// Handle FetchOrders event
  Future<void> _onFetchOrders(
    FetchOrders event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(status: OrderBlocStatus.loading));
    
    final result = await orderRepository.getOrders();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        orders: orders,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle FetchMoreOrders event
  Future<void> _onFetchMoreOrders(
    FetchMoreOrders event,
    Emitter<OrderState> emit,
  ) async {
    if (!state.hasNextPage) return;
    
    // Logic for pagination would be implemented here
    // For now we'll just fetch all orders again
    final result = await orderRepository.getOrders();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        orders: [...state.orders, ...orders],
        hasNextPage: orders.isNotEmpty,
      )),
    );
  }
  
  /// Handle FetchOrderById event
  Future<void> _onFetchOrderById(
    FetchOrderById event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(status: OrderBlocStatus.loading));
    
    final result = await orderRepository.getOrderById(event.orderId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (order) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        selectedOrder: order,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle CreateOrder event
  Future<void> _onCreateOrder(
    CreateOrder event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(status: OrderBlocStatus.processingOrder));
    
    final result = await orderRepository.createOrder(event.order);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (order) {
        final updatedOrders = [...state.orders, order];
        emit(state.copyWith(
          status: OrderBlocStatus.orderProcessed,
          orders: updatedOrders,
          selectedOrder: order,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle UpdateOrder event
  Future<void> _onUpdateOrder(
    UpdateOrder event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(status: OrderBlocStatus.updatingOrder));
    
    final result = await orderRepository.updateOrder(event.order);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (updatedOrder) {
        final updatedOrders = state.orders.map((order) {
          return order.id == updatedOrder.id ? updatedOrder : order;
        }).toList();
        
        emit(state.copyWith(
          status: OrderBlocStatus.orderUpdated,
          orders: updatedOrders,
          selectedOrder: updatedOrder,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle UpdateOrderStatus event
  Future<void> _onUpdateOrderStatus(
    UpdateOrderStatus event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(status: OrderBlocStatus.updatingOrder));
    
    final result = await orderRepository.updateOrderStatus(
      event.orderId, 
      event.status,
    );
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (updatedOrder) {
        final updatedOrders = state.orders.map((order) {
          return order.id == updatedOrder.id ? updatedOrder : order;
        }).toList();
        
        emit(state.copyWith(
          status: OrderBlocStatus.orderUpdated,
          orders: updatedOrders,
          selectedOrder: state.selectedOrder?.id == updatedOrder.id
              ? updatedOrder
              : state.selectedOrder,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle DeleteOrder event
  Future<void> _onDeleteOrder(
    DeleteOrder event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(status: OrderBlocStatus.deletingOrder));
    
    final result = await orderRepository.deleteOrder(event.orderId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (success) {
        if (success) {
          final updatedOrders = state.orders
              .where((order) => order.id != event.orderId)
              .toList();
          
          emit(state.copyWith(
            status: OrderBlocStatus.orderDeleted,
            orders: updatedOrders,
            selectedOrder: state.selectedOrder?.id == event.orderId
                ? null
                : state.selectedOrder,
            errorMessage: null,
          ));
        } else {
          emit(state.copyWith(
            status: OrderBlocStatus.error,
            errorMessage: 'Failed to delete order',
          ));
        }
      },
    );
  }
  
  /// Handle SearchOrders event
  Future<void> _onSearchOrders(
    SearchOrders event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(
      status: OrderBlocStatus.loading,
      isSearching: true,
      searchQuery: event.query,
    ));
    
    final result = await orderRepository.searchOrders(event.query);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        orders: orders,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle ClearOrderSearch event
  Future<void> _onClearOrderSearch(
    ClearOrderSearch event,
    Emitter<OrderState> emit,
  ) async {
    if (!state.isSearching) return;
    
    emit(state.copyWith(
      status: OrderBlocStatus.loading,
      isSearching: false,
      searchQuery: '',
    ));
    
    _fetchOrdersWithCurrentFilters(emit);
  }
  
  /// Handle FilterOrdersByStatus event
  Future<void> _onFilterOrdersByStatus(
    FilterOrdersByStatus event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(
      status: OrderBlocStatus.loading,
      isFiltering: true,
      filterStatus: event.status,
    ));
    
    final result = await orderRepository.getOrdersByStatus(event.status);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        orders: orders,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle FilterOrdersByCustomer event
  Future<void> _onFilterOrdersByCustomer(
    FilterOrdersByCustomer event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(
      status: OrderBlocStatus.loading,
      isFiltering: true,
      filterCustomerId: event.customerId,
    ));
    
    final result = await orderRepository.getOrdersByCustomerId(event.customerId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        orders: orders,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle FilterOrdersByDateRange event
  Future<void> _onFilterOrdersByDateRange(
    FilterOrdersByDateRange event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(
      status: OrderBlocStatus.loading,
      isFiltering: true,
      startDate: event.startDate,
      endDate: event.endDate,
    ));
    
    final result = await orderRepository.getOrdersByDateRange(
      event.startDate, 
      event.endDate,
    );
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        orders: orders,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle ClearOrderFilters event
  Future<void> _onClearOrderFilters(
    ClearOrderFilters event,
    Emitter<OrderState> emit,
  ) async {
    if (!state.isFiltering) return;
    
    emit(state.copyWith(
      status: OrderBlocStatus.loading,
      isFiltering: false,
      filterStatus: null,
      filterCustomerId: null,
      startDate: null,
      endDate: null,
    ));
    
    final result = await orderRepository.getOrders();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        orders: orders,
        errorMessage: null,
      )),
    );
  }
  
  /// Helper to fetch orders with current filters applied
  Future<void> _fetchOrdersWithCurrentFilters(Emitter<OrderState> emit) async {
    if (state.isSearching && state.searchQuery.isNotEmpty) {
      final result = await orderRepository.searchOrders(state.searchQuery);
      _handleOrderResult(result, emit);
    } else if (state.filterStatus != null) {
      final result = await orderRepository.getOrdersByStatus(state.filterStatus!);
      _handleOrderResult(result, emit);
    } else if (state.filterCustomerId != null) {
      final result = await orderRepository.getOrdersByCustomerId(state.filterCustomerId!);
      _handleOrderResult(result, emit);
    } else if (state.startDate != null && state.endDate != null) {
      final result = await orderRepository.getOrdersByDateRange(
        state.startDate!,
        state.endDate!,
      );
      _handleOrderResult(result, emit);
    } else {
      final result = await orderRepository.getOrders();
      _handleOrderResult(result, emit);
    }
  }
  
  /// Helper to handle order result and emit appropriate state
  void _handleOrderResult(
    dynamic result,
    Emitter<OrderState> emit,
  ) {
    result.fold(
      (failure) => emit(state.copyWith(
        status: OrderBlocStatus.error,
        errorMessage: failure.message,
      )),
      (orders) => emit(state.copyWith(
        status: OrderBlocStatus.loaded,
        orders: orders,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle RefreshOrders event
  Future<void> _onRefreshOrders(
    RefreshOrders event,
    Emitter<OrderState> emit,
  ) async {
    emit(state.copyWith(status: OrderBlocStatus.loading));
    
    _fetchOrdersWithCurrentFilters(emit);
  }
  
  /// Handle SelectOrder event
  void _onSelectOrder(
    SelectOrder event,
    Emitter<OrderState> emit,
  ) {
    emit(state.copyWith(selectedOrder: event.order));
  }
  
  /// Handle ClearSelectedOrder event
  void _onClearSelectedOrder(
    ClearSelectedOrder event,
    Emitter<OrderState> emit,
  ) {
    emit(state.copyWith(selectedOrder: null));
  }
}