import 'package:equatable/equatable.dart';

import '../../../domain/entities/order.dart';
import '../../../domain/entities/order.dart' show OrderStatus;

/// Base class for order events
abstract class OrderEvent extends Equatable {
  const OrderEvent();

  @override
  List<Object?> get props => [];
}

/// Event to fetch orders
class FetchOrders extends OrderEvent {
  const FetchOrders();
}

/// Event to fetch more orders (pagination)
class FetchMoreOrders extends OrderEvent {
  const FetchMoreOrders();
}

/// Event to fetch a single order by ID
class FetchOrderById extends OrderEvent {
  final String orderId;
  
  const FetchOrderById(this.orderId);
  
  @override
  List<Object> get props => [orderId];
}

/// Event to create a new order
class CreateOrder extends OrderEvent {
  final Order order;
  
  const CreateOrder(this.order);
  
  @override
  List<Object> get props => [order];
}

/// Event to update an existing order
class UpdateOrder extends OrderEvent {
  final Order order;
  
  const UpdateOrder(this.order);
  
  @override
  List<Object> get props => [order];
}

/// Event to update an order's status
class UpdateOrderStatus extends OrderEvent {
  final String orderId;
  final OrderStatus status;
  
  const UpdateOrderStatus(this.orderId, this.status);
  
  @override
  List<Object> get props => [orderId, status];
}

/// Event to delete an order
class DeleteOrder extends OrderEvent {
  final String orderId;
  
  const DeleteOrder(this.orderId);
  
  @override
  List<Object> get props => [orderId];
}

/// Event to search for orders
class SearchOrders extends OrderEvent {
  final String query;
  
  const SearchOrders(this.query);
  
  @override
  List<Object> get props => [query];
}

/// Event to clear search
class ClearOrderSearch extends OrderEvent {
  const ClearOrderSearch();
}

/// Event to filter orders by status
class FilterOrdersByStatus extends OrderEvent {
  final OrderStatus status;
  
  const FilterOrdersByStatus(this.status);
  
  @override
  List<Object> get props => [status];
}

/// Event to filter orders by customer ID
class FilterOrdersByCustomer extends OrderEvent {
  final String customerId;
  
  const FilterOrdersByCustomer(this.customerId);
  
  @override
  List<Object> get props => [customerId];
}

/// Event to filter orders by date range
class FilterOrdersByDateRange extends OrderEvent {
  final DateTime startDate;
  final DateTime endDate;
  
  const FilterOrdersByDateRange(this.startDate, this.endDate);
  
  @override
  List<Object> get props => [startDate, endDate];
}

/// Event to clear all filters
class ClearOrderFilters extends OrderEvent {
  const ClearOrderFilters();
}

/// Event to refresh order data
class RefreshOrders extends OrderEvent {
  const RefreshOrders();
}

/// Event to select an order
class SelectOrder extends OrderEvent {
  final Order order;
  
  const SelectOrder(this.order);
  
  @override
  List<Object> get props => [order];
}

/// Event to clear selected order
class ClearSelectedOrder extends OrderEvent {
  const ClearSelectedOrder();
}