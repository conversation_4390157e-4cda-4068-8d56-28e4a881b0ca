import 'package:equatable/equatable.dart';

import '../../../domain/entities/order.dart' as entity;

/// Order bloc states
enum OrderBlocStatus {
  initial,
  loading,
  loaded,
  error,
  processingOrder,
  orderProcessed,
  updatingOrder,
  orderUpdated,
  deletingOrder,
  orderDeleted,
}

/// State for the order bloc
class OrderState extends Equatable {
  final OrderBlocStatus status;
  final List<entity.Order> orders;
  final entity.Order? selectedOrder;
  final String? errorMessage;
  final bool hasNextPage;
  final bool isSearching;
  final String searchQuery;
  final entity.OrderStatus? filterStatus;
  final bool isFiltering;
  final String? filterCustomerId;
  final DateTime? startDate;
  final DateTime? endDate;
  
  const OrderState({
    this.status = OrderBlocStatus.initial,
    this.orders = const [],
    this.selectedOrder,
    this.errorMessage,
    this.hasNextPage = false,
    this.isSearching = false,
    this.searchQuery = '',
    this.filterStatus,
    this.isFiltering = false,
    this.filterCustomerId,
    this.startDate,
    this.endDate,
  });
  
  /// Creates a copy of this state with the given fields replaced
  OrderState copyWith({
    OrderBlocStatus? status,
    List<entity.Order>? orders,
    entity.Order? selectedOrder,
    String? errorMessage,
    bool? hasNextPage,
    bool? isSearching,
    String? searchQuery,
    entity.OrderStatus? filterStatus,
    bool? isFiltering,
    String? filterCustomerId,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return OrderState(
      status: status ?? this.status,
      orders: orders ?? this.orders,
      selectedOrder: selectedOrder ?? this.selectedOrder,
      errorMessage: errorMessage,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
      filterStatus: filterStatus ?? this.filterStatus,
      isFiltering: isFiltering ?? this.isFiltering,
      filterCustomerId: filterCustomerId ?? this.filterCustomerId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
  
  @override
  List<Object?> get props => [
    status,
    orders,
    selectedOrder,
    errorMessage,
    hasNextPage,
    isSearching,
    searchQuery,
    filterStatus,
    isFiltering,
    filterCustomerId,
    startDate,
    endDate,
  ];
}