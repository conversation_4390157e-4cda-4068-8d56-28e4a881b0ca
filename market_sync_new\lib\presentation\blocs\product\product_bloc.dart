import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/repositories/product_repository.dart';
import 'product_event.dart';
import 'product_state.dart';

/// Bloc for managing product state
class ProductBloc extends Bloc<ProductEvent, ProductState> {
  final ProductRepository productRepository;
  
  ProductBloc({required this.productRepository}) : super(const ProductState()) {
    on<FetchProducts>(_onFetchProducts);
    on<FetchMoreProducts>(_onFetchMoreProducts);
    on<FetchProductById>(_onFetchProductById);
    on<CreateProduct>(_onCreateProduct);
    on<UpdateProduct>(_onUpdateProduct);
    on<DeleteProduct>(_onDeleteProduct);
    on<SearchProducts>(_onSearchProducts);
    on<ClearSearch>(_onClearSearch);
    on<FilterProductsByCategory>(_onFilterProductsByCategory);
    on<ClearFilter>(_onClearFilter);
    on<RefreshProducts>(_onRefreshProducts);
    on<SelectProduct>(_onSelectProduct);
    on<ClearSelectedProduct>(_onClearSelectedProduct);
  }
  
  /// Handle FetchProducts event
  Future<void> _onFetchProducts(
    FetchProducts event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.loading));
    
    final result = await productRepository.getProducts();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductStatus.loaded,
        products: products,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle FetchMoreProducts event
  Future<void> _onFetchMoreProducts(
    FetchMoreProducts event,
    Emitter<ProductState> emit,
  ) async {
    if (!state.hasNextPage) return;
    
    // Logic for pagination would be implemented here
    // For now we'll just fetch all products again
    final result = await productRepository.getProducts();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        products: [...state.products, ...products],
        hasNextPage: products.isNotEmpty,
      )),
    );
  }
  
  /// Handle FetchProductById event
  Future<void> _onFetchProductById(
    FetchProductById event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.loading));
    
    final result = await productRepository.getProductById(event.productId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (product) => emit(state.copyWith(
        status: ProductStatus.loaded,
        selectedProduct: product,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle CreateProduct event
  Future<void> _onCreateProduct(
    CreateProduct event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.addingProduct));
    
    final result = await productRepository.createProduct(event.product);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (product) {
        final updatedProducts = [...state.products, product];
        emit(state.copyWith(
          status: ProductStatus.productAdded,
          products: updatedProducts,
          selectedProduct: product,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle UpdateProduct event
  Future<void> _onUpdateProduct(
    UpdateProduct event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.updatingProduct));
    
    final result = await productRepository.updateProduct(event.product);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (updatedProduct) {
        final updatedProducts = state.products.map((product) {
          return product.id == updatedProduct.id ? updatedProduct : product;
        }).toList();
        
        emit(state.copyWith(
          status: ProductStatus.productUpdated,
          products: updatedProducts,
          selectedProduct: updatedProduct,
          errorMessage: null,
        ));
      },
    );
  }
  
  /// Handle DeleteProduct event
  Future<void> _onDeleteProduct(
    DeleteProduct event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.deletingProduct));
    
    final result = await productRepository.deleteProduct(event.productId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (success) {
        if (success) {
          final updatedProducts = state.products
              .where((product) => product.id != event.productId)
              .toList();
          
          emit(state.copyWith(
            status: ProductStatus.productDeleted,
            products: updatedProducts,
            selectedProduct: state.selectedProduct?.id == event.productId
                ? null
                : state.selectedProduct,
            errorMessage: null,
          ));
        } else {
          emit(state.copyWith(
            status: ProductStatus.error,
            errorMessage: 'Failed to delete product',
          ));
        }
      },
    );
  }
  
  /// Handle SearchProducts event
  Future<void> _onSearchProducts(
    SearchProducts event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(
      status: ProductStatus.loading,
      isSearching: true,
      searchQuery: event.query,
    ));
    
    final result = await productRepository.searchProducts(event.query);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductStatus.loaded,
        products: products,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle ClearSearch event
  Future<void> _onClearSearch(
    ClearSearch event,
    Emitter<ProductState> emit,
  ) async {
    if (!state.isSearching) return;
    
    emit(state.copyWith(
      status: ProductStatus.loading,
      isSearching: false,
      searchQuery: '',
    ));
    
    final result = await productRepository.getProducts();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductStatus.loaded,
        products: products,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle FilterProductsByCategory event
  Future<void> _onFilterProductsByCategory(
    FilterProductsByCategory event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(
      status: ProductStatus.loading,
      isFiltering: true,
      filterCategory: event.categoryId,
    ));
    
    final result = await productRepository.getProductsByCategoryId(event.categoryId);
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductStatus.loaded,
        products: products,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle ClearFilter event
  Future<void> _onClearFilter(
    ClearFilter event,
    Emitter<ProductState> emit,
  ) async {
    if (!state.isFiltering) return;
    
    emit(state.copyWith(
      status: ProductStatus.loading,
      isFiltering: false,
      filterCategory: null,
    ));
    
    final result = await productRepository.getProducts();
    
    result.fold(
      (failure) => emit(state.copyWith(
        status: ProductStatus.error,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: ProductStatus.loaded,
        products: products,
        errorMessage: null,
      )),
    );
  }
  
  /// Handle RefreshProducts event
  Future<void> _onRefreshProducts(
    RefreshProducts event,
    Emitter<ProductState> emit,
  ) async {
    final isSearching = state.isSearching;
    final isFiltering = state.isFiltering;
    final searchQuery = state.searchQuery;
    final filterCategory = state.filterCategory;
    
    emit(state.copyWith(status: ProductStatus.loading));
    
    if (isSearching && searchQuery.isNotEmpty) {
      final result = await productRepository.searchProducts(searchQuery);
      
      result.fold(
        (failure) => emit(state.copyWith(
          status: ProductStatus.error,
          errorMessage: failure.message,
        )),
        (products) => emit(state.copyWith(
          status: ProductStatus.loaded,
          products: products,
          errorMessage: null,
        )),
      );
    } else if (isFiltering && filterCategory != null) {
      final result = await productRepository.getProductsByCategoryId(filterCategory);
      
      result.fold(
        (failure) => emit(state.copyWith(
          status: ProductStatus.error,
          errorMessage: failure.message,
        )),
        (products) => emit(state.copyWith(
          status: ProductStatus.loaded,
          products: products,
          errorMessage: null,
        )),
      );
    } else {
      final result = await productRepository.getProducts();
      
      result.fold(
        (failure) => emit(state.copyWith(
          status: ProductStatus.error,
          errorMessage: failure.message,
        )),
        (products) => emit(state.copyWith(
          status: ProductStatus.loaded,
          products: products,
          errorMessage: null,
        )),
      );
    }
  }
  
  /// Handle SelectProduct event
  void _onSelectProduct(
    SelectProduct event,
    Emitter<ProductState> emit,
  ) {
    emit(state.copyWith(selectedProduct: event.product));
  }
  
  /// Handle ClearSelectedProduct event
  void _onClearSelectedProduct(
    ClearSelectedProduct event,
    Emitter<ProductState> emit,
  ) {
    emit(state.copyWith(selectedProduct: null));
  }
}