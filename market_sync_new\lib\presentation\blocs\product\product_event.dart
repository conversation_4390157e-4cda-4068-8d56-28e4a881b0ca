import 'package:equatable/equatable.dart';

import '../../../domain/entities/product.dart';

/// Base class for product events
abstract class ProductEvent extends Equatable {
  const ProductEvent();

  @override
  List<Object?> get props => [];
}

/// Event to fetch products
class FetchProducts extends ProductEvent {
  const FetchProducts();
}

/// Event to fetch more products (pagination)
class FetchMoreProducts extends ProductEvent {
  const FetchMoreProducts();
}

/// Event to fetch a single product by ID
class FetchProductById extends ProductEvent {
  final String productId;
  
  const FetchProductById(this.productId);
  
  @override
  List<Object> get props => [productId];
}

/// Event to create a new product
class CreateProduct extends ProductEvent {
  final Product product;
  
  const CreateProduct(this.product);
  
  @override
  List<Object> get props => [product];
}

/// Event to update an existing product
class UpdateProduct extends ProductEvent {
  final Product product;
  
  const UpdateProduct(this.product);
  
  @override
  List<Object> get props => [product];
}

/// Event to delete a product
class DeleteProduct extends ProductEvent {
  final String productId;
  
  const DeleteProduct(this.productId);
  
  @override
  List<Object> get props => [productId];
}

/// Event to search for products
class SearchProducts extends ProductEvent {
  final String query;
  
  const SearchProducts(this.query);
  
  @override
  List<Object> get props => [query];
}

/// Event to clear search
class ClearSearch extends ProductEvent {
  const ClearSearch();
}

/// Event to filter products by category
class FilterProductsByCategory extends ProductEvent {
  final String categoryId;
  
  const FilterProductsByCategory(this.categoryId);
  
  @override
  List<Object> get props => [categoryId];
}

/// Event to clear filter
class ClearFilter extends ProductEvent {
  const ClearFilter();
}

/// Event to refresh product data
class RefreshProducts extends ProductEvent {
  const RefreshProducts();
}

/// Event to select a product
class SelectProduct extends ProductEvent {
  final Product product;
  
  const SelectProduct(this.product);
  
  @override
  List<Object> get props => [product];
}

/// Event to clear selected product
class ClearSelectedProduct extends ProductEvent {
  const ClearSelectedProduct();
}