import 'package:equatable/equatable.dart';

import '../../../domain/entities/product.dart';

/// Product states
enum ProductStatus {
  initial,
  loading,
  loaded,
  error,
  addingProduct,
  productAdded,
  updatingProduct,
  productUpdated,
  deletingProduct,
  productDeleted,
}

/// State for the product bloc
class ProductState extends Equatable {
  final ProductStatus status;
  final List<Product> products;
  final Product? selectedProduct;
  final String? errorMessage;
  final bool hasNextPage;
  final bool isSearching;
  final String searchQuery;
  final String? filterCategory;
  final bool isFiltering;
  
  const ProductState({
    this.status = ProductStatus.initial,
    this.products = const [],
    this.selectedProduct,
    this.errorMessage,
    this.hasNextPage = false,
    this.isSearching = false,
    this.searchQuery = '',
    this.filterCategory,
    this.isFiltering = false,
  });
  
  /// Creates a copy of this state with the given fields replaced
  ProductState copyWith({
    ProductStatus? status,
    List<Product>? products,
    Product? selectedProduct,
    String? errorMessage,
    bool? hasNextPage,
    bool? isSearching,
    String? searchQuery,
    String? filterCategory,
    bool? isFiltering,
  }) {
    return ProductState(
      status: status ?? this.status,
      products: products ?? this.products,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      errorMessage: errorMessage,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
      filterCategory: filterCategory ?? this.filterCategory,
      isFiltering: isFiltering ?? this.isFiltering,
    );
  }
  
  @override
  List<Object?> get props => [
    status,
    products,
    selectedProduct,
    errorMessage,
    hasNextPage,
    isSearching,
    searchQuery,
    filterCategory,
    isFiltering,
  ];
}