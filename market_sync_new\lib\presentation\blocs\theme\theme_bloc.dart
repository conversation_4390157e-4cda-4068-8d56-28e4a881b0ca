import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'theme_event.dart';
import 'theme_state.dart';

/// Bloc that manages theme settings
class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final SharedPreferences sharedPreferences;
  final String themeModeKey;

  ThemeBloc({
    required this.sharedPreferences,
    required this.themeModeKey,
  }) : super(ThemeState.initial()) {
    on<LoadThemeEvent>(_onLoadTheme);
    on<ChangeThemeEvent>(_onChangeTheme);
    on<ToggleThemeEvent>(_onToggleTheme);
  }

  /// Load the saved theme from shared preferences
  void _onLoadTheme(LoadThemeEvent event, Emitter<ThemeState> emit) {
    final String? themeModeString = sharedPreferences.getString(themeModeKey);
    
    if (themeModeString != null) {
      final ThemeMode themeMode = _getThemeModeFromString(themeModeString);
      emit(state.copyWith(themeMode: themeMode));
    }
  }

  /// Change the theme mode and save it to shared preferences
  void _onChangeTheme(ChangeThemeEvent event, Emitter<ThemeState> emit) async {
    await sharedPreferences.setString(
      themeModeKey, 
      _getStringFromThemeMode(event.themeMode)
    );
    
    emit(state.copyWith(themeMode: event.themeMode));
  }

  /// Toggle between light and dark theme
  void _onToggleTheme(ToggleThemeEvent event, Emitter<ThemeState> emit) async {
    final ThemeMode newThemeMode = state.themeMode == ThemeMode.light
        ? ThemeMode.dark
        : ThemeMode.light;
    
    await sharedPreferences.setString(
      themeModeKey, 
      _getStringFromThemeMode(newThemeMode)
    );
    
    emit(state.copyWith(themeMode: newThemeMode));
  }

  /// Convert a string to ThemeMode
  ThemeMode _getThemeModeFromString(String themeModeString) {
    switch (themeModeString) {
      case 'dark':
        return ThemeMode.dark;
      case 'light':
        return ThemeMode.light;
      case 'system':
        return ThemeMode.system;
      default:
        return ThemeMode.light;
    }
  }

  /// Convert ThemeMode to a string
  String _getStringFromThemeMode(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.light:
        return 'light';
      case ThemeMode.system:
        return 'system';
    }
  }
}