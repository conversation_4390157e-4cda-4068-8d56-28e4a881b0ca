import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Represents the state of the theme bloc
class ThemeState extends Equatable {
  final ThemeMode themeMode;

  const ThemeState({required this.themeMode});

  /// Initial state with the default theme mode
  factory ThemeState.initial() => const ThemeState(themeMode: ThemeMode.light);

  /// Creates a copy of this state with the given fields replaced
  ThemeState copyWith({ThemeMode? themeMode}) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
    );
  }

  @override
  List<Object?> get props => [themeMode];
}