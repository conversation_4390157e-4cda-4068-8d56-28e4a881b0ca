import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../core/constants/app_constants.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_state.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/dashboard/dashboard_screen.dart';
import '../screens/splash/splash_screen.dart';
import '../screens/error/error_screen.dart';
import '../screens/product/products_screen.dart';
import '../screens/product/product_details_screen.dart';
import '../screens/product/product_form_screen.dart';
import '../screens/order/orders_screen.dart';
import '../screens/order/order_details_screen.dart';
import '../screens/product/barcode_scanner_screen.dart';
import '../screens/product/categories_screen.dart';
import '../screens/order/orders_management_screen.dart';
import '../screens/order/order_details_enhanced_screen.dart';
import '../screens/notifications/notifications_screen.dart';
import '../screens/profile/profile_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/role_management/role_management_screen.dart';

/// Class responsible for app routing configuration
class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  /// The router instance
  static final router = GoRouter(
    initialLocation: AppConstants.splashRoute,
    navigatorKey: _rootNavigatorKey,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authBloc = context.read<AuthBloc>();
      final isAuthenticated = authBloc.state.status == AuthStatus.authenticated;
      final isSplash = state.matchedLocation == AppConstants.splashRoute;
      final isLoginRoute = state.matchedLocation == AppConstants.loginRoute;
      final isRegisterRoute = state.matchedLocation == AppConstants.registerRoute;

      // Always allow splash screen
      if (isSplash) return null;

      // If not authenticated, redirect to login unless already on login or register
      if (!isAuthenticated && !isLoginRoute && !isRegisterRoute) {
        return AppConstants.loginRoute;
      }

      // If authenticated and on login, redirect to dashboard
      if (isAuthenticated && (isLoginRoute || isRegisterRoute)) {
        return AppConstants.dashboardRoute;
      }

      // No redirection needed
      return null;
    },
    routes: [
      // Splash
      GoRoute(
        path: AppConstants.splashRoute,
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Authentication
      GoRoute(
        path: AppConstants.loginRoute,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppConstants.registerRoute,
        builder: (context, state) => const RegisterScreen(),
      ),
      
      // Dashboard
      GoRoute(
        path: AppConstants.dashboardRoute,
        builder: (context, state) => const DashboardScreen(),
      ),
      
      // Profile
      GoRoute(
        path: AppConstants.profileRoute,
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Profile Screen - To be implemented')),
        ),
      ),
      
      // Settings
      GoRoute(
        path: AppConstants.settingsRoute,
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Settings Screen - To be implemented')),
        ),
      ),
      
      // Product routes
      GoRoute(
        path: AppConstants.productsRoute,
        builder: (context, state) => const ProductsScreen(),
      ),
      GoRoute(
        path: '${AppConstants.productDetailsRoute}/:id',
        builder: (context, state) => ProductDetailsScreen(
          productId: state.pathParameters['id'] ?? '',
        ),
      ),
      GoRoute(
        path: AppConstants.addProductRoute,
        builder: (context, state) => const ProductFormScreen(),
      ),
      GoRoute(
        path: '${AppConstants.editProductRoute}/:id',
        builder: (context, state) => ProductFormScreen(
          productId: state.pathParameters['id'],
        ),
      ),
      
      // Order routes
      GoRoute(
        path: AppConstants.ordersRoute,
        builder: (context, state) => const OrdersScreen(),
      ),
      GoRoute(
        path: '${AppConstants.orderDetailsRoute}/:id',
        builder: (context, state) => OrderDetailsScreen(
          orderId: state.pathParameters['id'] ?? '',
        ),
      ),
      
      // Barcode scanner
      GoRoute(
        path: '/barcode-scanner',
        builder: (context, state) => const BarcodeScannerScreen(),
      ),
      
      // Categories management
      GoRoute(
        path: '/categories',
        builder: (context, state) => const CategoriesScreen(),
      ),
      
      // Orders management
      GoRoute(
        path: '/orders-management',
        builder: (context, state) => const OrdersManagementScreen(),
      ),
      
      // Enhanced order details
      GoRoute(
        path: '/order-details-enhanced/:orderId',
        builder: (context, state) => OrderDetailsEnhancedScreen(
          orderId: state.pathParameters['orderId']!,
        ),
      ),
      
      // Notifications
      GoRoute(
        path: '/notifications',
        builder: (context, state) => const NotificationsScreen(),
      ),
      
      // Profile
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      
      // Settings
      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsScreen(),
      ),
      
      // Role Management
      GoRoute(
        path: '/role-management',
        builder: (context, state) => const RoleManagementScreen(),
      ),
      
      // Owner routes
      GoRoute(
        path: '/owner/inventory',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Owner Inventory Screen - To be implemented')),
        ),
      ),
      GoRoute(
        path: '/owner/products',
        builder: (context, state) => const ProductsScreen(),
      ),
      GoRoute(
        path: '/owner/orders',
        builder: (context, state) => const OrdersScreen(),
      ),
      
      // Employee routes
      GoRoute(
        path: '/employee/dashboard',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Employee Dashboard Screen - To be implemented')),
        ),
      ),
      GoRoute(
        path: '/employee/tasks',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Employee Tasks Screen - To be implemented')),
        ),
      ),
      
      // Customer routes
      GoRoute(
        path: '/customer/shop',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Customer Shop Screen - To be implemented')),
        ),
      ),
      GoRoute(
        path: '/customer/orders',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Customer Orders Screen - To be implemented')),
        ),
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );
}