import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/user.dart';
import '../../blocs/customer/customer_bloc.dart';
import '../../blocs/customer/customer_event.dart';
import '../../blocs/customer/customer_state.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/customer/customer_card.dart';
import '../../widgets/customer/customer_search_bar.dart';

/// Screen for displaying and managing customers
class CustomersScreen extends StatefulWidget {
  const CustomersScreen({Key? key}) : super(key: key);

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    // Fetch customers when screen is first loaded
    context.read<CustomerBloc>().add(const FetchCustomers());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<CustomerBloc>().add(const FetchMoreCustomers());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppConstants.customersTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<CustomerBloc>().add(const RefreshCustomers());
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter controls
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              children: [
                CustomerSearchBar(
                  onSearch: (query) {
                    context.read<CustomerBloc>().add(SearchCustomers(query));
                  },
                  onClear: () {
                    context.read<CustomerBloc>().add(const ClearCustomerSearch());
                  },
                ),
                SizedBox(height: 8.h),
                SizedBox(
                  height: 40.h,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      _buildRoleFilter(context, 'All', null),
                      SizedBox(width: 8.w),
                      _buildRoleFilter(context, 'Retail', 'customer'),
                      SizedBox(width: 8.w),
                      _buildRoleFilter(context, 'Wholesale', 'wholesaler'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Customers list
          Expanded(
            child: BlocBuilder<CustomerBloc, CustomerState>(
              builder: (context, state) {
                if (state.status == CustomerBlocStatus.initial) {
                  return const LoadingIndicator();
                } else if (state.status == CustomerBlocStatus.loading && state.customers.isEmpty) {
                  return const LoadingIndicator();
                } else if (state.status == CustomerBlocStatus.error) {
                  return ErrorView(
                    message: state.errorMessage ?? 'Error loading customers',
                    onRetry: () {
                      context.read<CustomerBloc>().add(const FetchCustomers());
                    },
                  );
                } else if (state.customers.isEmpty) {
                  return EmptyView(
                    message: 'No customers found',
                    buttonText: 'Add Customer',
                    onActionPressed: () {
                      context.push(AppConstants.addCustomerRoute);
                    },
                  );
                }
                
                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<CustomerBloc>().add(const RefreshCustomers());
                  },
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.all(16.r),
                    itemCount: state.customers.length + (state.hasNextPage ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index >= state.customers.length) {
                        return Center(
                          child: Padding(
                            padding: EdgeInsets.all(16.r),
                            child: const CircularProgressIndicator(),
                          ),
                        );
                      }
                      
                      final customer = state.customers[index];
                      return Padding(
                        padding: EdgeInsets.only(bottom: 12.h),
                        child: CustomerCard(
                          customer: customer,
                          onTap: () {
                            context.read<CustomerBloc>().add(SelectCustomer(customer));
                            context.push('${AppConstants.customerDetailsRoute}/${customer.id}');
                          },
                          onEdit: () {
                            context.push('${AppConstants.editCustomerRoute}/${customer.id}');
                          },
                          onDelete: () {
                            _showDeleteConfirmation(context, customer);
                          },
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push(AppConstants.addCustomerRoute);
        },
        child: const Icon(Icons.add),
      ),
    );
  }
  
  Widget _buildRoleFilter(BuildContext context, String label, String? role) {
    return BlocBuilder<CustomerBloc, CustomerState>(
      builder: (context, state) {
        final isSelected = (role == null && state.filterRole == null) ||
            (role != null && state.filterRole == role);
        
        return FilterChip(
          label: Text(label),
          selected: isSelected,
          onSelected: (_) {
            if (role == null) {
              context.read<CustomerBloc>().add(const ClearCustomerFilters());
            } else {
              context.read<CustomerBloc>().add(FilterCustomersByRole(role));
            }
          },
          backgroundColor: Theme.of(context).colorScheme.surface,
          selectedColor: Theme.of(context).colorScheme.primaryContainer,
          checkmarkColor: Theme.of(context).colorScheme.onPrimaryContainer,
          labelStyle: TextStyle(
            color: isSelected
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
            side: BorderSide(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outline.withOpacity(0.5),
              width: 1,
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        );
      },
    );
  }
  
  void _showDeleteConfirmation(BuildContext context, User customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Customer'),
        content: Text('Are you sure you want to delete ${customer.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<CustomerBloc>().add(DeleteCustomer(customer.id));
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}