import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../domain/entities/user.dart';
import '../../blocs/auth/auth_bloc.dart';
import '../../blocs/auth/auth_event.dart';
import '../../blocs/auth/auth_state.dart';
import '../../widgets/dashboard/dashboard_card.dart';
import '../../widgets/dashboard/recent_activity.dart';
import '../../widgets/common/loading_indicator.dart';

/// Dashboard screen
class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final colorScheme = Theme.of(context).colorScheme;

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state.status != AuthStatus.authenticated) {
          return const Scaffold(body: Center(child: LoadingIndicator()));
        }

        final user = state.user!;
        return Scaffold(
          appBar: AppBar(
            title: Text(l10n.dashboard),
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {
                  // TODO: Navigate to notifications screen
                },
              ),
              IconButton(
                icon: const Icon(Icons.settings_outlined),
                onPressed: () {
                  context.go(AppConstants.settingsRoute);
                },
              ),
            ],
          ),
          drawer: _buildDrawer(context, user, l10n),
          body: _buildDashboardContent(context, user, l10n),
        );
      },
    );
  }

  Widget _buildDashboardContent(BuildContext context, User user, AppLocalizations l10n) {
    final theme = Theme.of(context);
    
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          // TODO: Implement refresh logic
        },
        child: ListView(
          padding: EdgeInsets.all(16.w),
          children: [
            // Welcome section
            Text(
              'Welcome back, ${user.name}!',
              style: theme.textTheme.headlineMedium,
            ),
            SizedBox(height: 8.h),
            Text(
              '${user.role.toString().split('.').last.toUpperCase()} Dashboard',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
            SizedBox(height: 24.h),
            
            // Dashboard cards
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 16.h,
              crossAxisSpacing: 16.w,
              childAspectRatio: 1.2,
              children: [
                DashboardCard(
                  title: l10n.orders,
                  value: '12',
                  icon: Icons.shopping_cart_outlined,
                  color: theme.colorScheme.primary,
                  onTap: () {
                    // TODO: Navigate to orders
                  },
                ),
                DashboardCard(
                  title: l10n.products,
                  value: '156',
                  icon: Icons.inventory_2_outlined,
                  color: theme.colorScheme.tertiary,
                  onTap: () {
                    // TODO: Navigate to products
                  },
                ),
                DashboardCard(
                  title: l10n.customers,
                  value: '48',
                  icon: Icons.people_outline,
                  color: Colors.orange,
                  onTap: () {
                    // TODO: Navigate to customers
                  },
                ),
                DashboardCard(
                  title: l10n.sales,
                  value: '\$2,450',
                  icon: Icons.attach_money,
                  color: Colors.purple,
                  onTap: () {
                    // TODO: Navigate to sales
                  },
                ),
              ],
            ),
            SizedBox(height: 32.h),
            
            // Recent activity
            Text(
              l10n.recentActivity,
              style: theme.textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            const RecentActivity(),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawer(BuildContext context, User user, AppLocalizations l10n) {
    final theme = Theme.of(context);
    
    return Drawer(
      child: SafeArea(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            // User info header
            Container(
              padding: EdgeInsets.all(16.w),
              color: theme.colorScheme.primary,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircleAvatar(
                    radius: 30.r,
                    backgroundColor: Colors.white,
                    child: Text(
                      user.name[0].toUpperCase(),
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    user.name,
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    user.role.toString().split('.').last,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            
            // Menu items
            ListTile(
              leading: const Icon(Icons.dashboard_outlined),
              title: Text(l10n.dashboard),
              selected: true,
              onTap: () {
                Navigator.pop(context);
              },
            ),
            
            _buildMenuItemBasedOnRole(user, l10n, context),
            
            const Divider(),
            
            // Settings
            ListTile(
              leading: const Icon(Icons.settings_outlined),
              title: Text(l10n.settings),
              onTap: () {
                Navigator.pop(context);
                context.go(AppConstants.settingsRoute);
              },
            ),
            
            // Profile
            ListTile(
              leading: const Icon(Icons.person_outline),
              title: Text(l10n.profile),
              onTap: () {
                Navigator.pop(context);
                context.go(AppConstants.profileRoute);
              },
            ),
            
            const Divider(),
            
            // Logout
            ListTile(
              leading: const Icon(Icons.logout),
              title: Text(l10n.logout),
              onTap: () {
                Navigator.pop(context);
                context.read<AuthBloc>().add(const LogoutEvent());
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItemBasedOnRole(User user, AppLocalizations l10n, BuildContext context) {
    if (user.isOwner) {
      return Column(
        children: [
          ListTile(
            leading: const Icon(Icons.inventory_2_outlined),
            title: Text(l10n.inventory),
            onTap: () {
              Navigator.pop(context);
              context.go('/owner/inventory');
            },
          ),
          ListTile(
            leading: const Icon(Icons.category_outlined),
            title: Text(l10n.products),
            onTap: () {
              Navigator.pop(context);
              context.go('/owner/products');
            },
          ),
          ListTile(
            leading: const Icon(Icons.shopping_cart_outlined),
            title: Text(l10n.orders),
            onTap: () {
              Navigator.pop(context);
              context.go('/owner/orders');
            },
          ),
        ],
      );
    } else if (user.isEmployee) {
      return Column(
        children: [
          ListTile(
            leading: const Icon(Icons.task_outlined),
            title: Text(l10n.tasks),
            onTap: () {
              Navigator.pop(context);
              context.go('/employee/tasks');
            },
          ),
        ],
      );
    } else if (user.isCustomer) {
      return Column(
        children: [
          ListTile(
            leading: const Icon(Icons.shopping_bag_outlined),
            title: Text(l10n.shop),
            onTap: () {
              Navigator.pop(context);
              context.go('/customer/shop');
            },
          ),
          ListTile(
            leading: const Icon(Icons.receipt_long_outlined),
            title: Text(l10n.orders),
            onTap: () {
              Navigator.pop(context);
              context.go('/customer/orders');
            },
          ),
        ],
      );
    }
    
    return const SizedBox.shrink();
  }
}