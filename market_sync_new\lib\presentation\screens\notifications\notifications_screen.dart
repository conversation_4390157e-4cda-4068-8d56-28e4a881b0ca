import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/services/notification_service.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/empty_view.dart';

/// Screen for displaying and managing notifications
class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final NotificationService _notificationService = NotificationService();
  
  // Mock notifications data - in real app, this would come from a bloc/provider
  final List<AppNotification> _notifications = [
    AppNotification(
      id: '1',
      title: 'طلب جديد 🛒',
      body: 'طلب جديد من أحمد محمد بقيمة \$125.50',
      type: NotificationType.order,
      isRead: false,
      createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
      data: {'orderId': 'ORD001', 'customerId': 'CUST001'},
    ),
    AppNotification(
      id: '2',
      title: 'مخزون منخفض ⚠️',
      body: 'المنتج "iPhone 14 Pro" مخزونه منخفض (5 متبقي)',
      type: NotificationType.product,
      isRead: false,
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      data: {'productId': 'PROD001'},
    ),
    AppNotification(
      id: '3',
      title: 'تم التسليم ✅',
      body: 'تم تسليم طلب فاطمة علي بنجاح',
      type: NotificationType.order,
      isRead: true,
      createdAt: DateTime.now().subtract(const Duration(hours: 4)),
      data: {'orderId': 'ORD002', 'customerId': 'CUST002'},
    ),
    AppNotification(
      id: '4',
      title: 'عميل جديد 👤',
      body: 'انضم عميل جديد: محمد السالم',
      type: NotificationType.customer,
      isRead: true,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      data: {'customerId': 'CUST003'},
    ),
    AppNotification(
      id: '5',
      title: 'دفعة مستلمة 💰',
      body: 'تم استلام دفعة من سارة أحمد بقيمة \$75.00',
      type: NotificationType.payment,
      isRead: true,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      data: {'paymentId': 'PAY001', 'customerId': 'CUST004'},
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _notificationService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.mark_email_read),
            onPressed: _markAllAsRead,
            tooltip: 'تحديد الكل كمقروء',
          ),
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            onPressed: _clearAllNotifications,
            tooltip: 'مسح جميع الإشعارات',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showNotificationSettings,
            tooltip: 'إعدادات الإشعارات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(text: 'الكل (${_notifications.length})'),
            Tab(text: 'غير مقروءة (${_getUnreadCount()})'),
            Tab(text: 'الطلبات (${_getNotificationsByType(NotificationType.order).length})'),
            Tab(text: 'المنتجات (${_getNotificationsByType(NotificationType.product).length})'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationsList(_notifications),
          _buildNotificationsList(_getUnreadNotifications()),
          _buildNotificationsList(_getNotificationsByType(NotificationType.order)),
          _buildNotificationsList(_getNotificationsByType(NotificationType.product)),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(List<AppNotification> notifications) {
    if (notifications.isEmpty) {
      return const EmptyView(
        message: 'لا توجد إشعارات',
        icon: Icons.notifications_none,
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: _buildNotificationCard(notification),
        );
      },
    );
  }

  Widget _buildNotificationCard(AppNotification notification) {
    return Card(
      elevation: notification.isRead ? 1 : 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () => _onNotificationTap(notification),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            color: notification.isRead 
                ? null 
                : Theme.of(context).colorScheme.primaryContainer.withOpacity(0.1),
          ),
          child: Row(
            children: [
              // Notification icon
              Container(
                width: 48.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification.type).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 24.sp,
                ),
              ),
              
              SizedBox(width: 16.w),
              
              // Notification content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8.w,
                            height: 8.h,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    
                    SizedBox(height: 4.h),
                    
                    Text(
                      notification.body,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    SizedBox(height: 8.h),
                    
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14.sp,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          timeago.format(notification.createdAt, locale: 'ar'),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const Spacer(),
                        _buildNotificationTypeChip(notification.type),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Actions
              PopupMenuButton<String>(
                onSelected: (value) => _onNotificationAction(notification, value),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'mark_read',
                    child: ListTile(
                      leading: Icon(notification.isRead ? Icons.mark_email_unread : Icons.mark_email_read),
                      title: Text(notification.isRead ? 'تحديد كغير مقروء' : 'تحديد كمقروء'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('حذف', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationTypeChip(NotificationType type) {
    String text;
    Color color;
    
    switch (type) {
      case NotificationType.order:
        text = 'طلب';
        color = Colors.blue;
        break;
      case NotificationType.product:
        text = 'منتج';
        color = Colors.green;
        break;
      case NotificationType.customer:
        text = 'عميل';
        color = Colors.purple;
        break;
      case NotificationType.payment:
        text = 'دفعة';
        color = Colors.orange;
        break;
      case NotificationType.system:
        text = 'نظام';
        color = Colors.grey;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Icons.shopping_cart;
      case NotificationType.product:
        return Icons.inventory;
      case NotificationType.customer:
        return Icons.person;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.system:
        return Icons.settings;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Colors.blue;
      case NotificationType.product:
        return Colors.green;
      case NotificationType.customer:
        return Colors.purple;
      case NotificationType.payment:
        return Colors.orange;
      case NotificationType.system:
        return Colors.grey;
    }
  }

  void _onNotificationTap(AppNotification notification) {
    // Mark as read
    setState(() {
      notification.isRead = true;
    });

    // Navigate based on notification type and data
    switch (notification.type) {
      case NotificationType.order:
        if (notification.data['orderId'] != null) {
          // Navigate to order details
          // context.push('/order-details/${notification.data['orderId']}');
        }
        break;
      case NotificationType.product:
        if (notification.data['productId'] != null) {
          // Navigate to product details
          // context.push('/product-details/${notification.data['productId']}');
        }
        break;
      case NotificationType.customer:
        if (notification.data['customerId'] != null) {
          // Navigate to customer details
          // context.push('/customer-details/${notification.data['customerId']}');
        }
        break;
      default:
        break;
    }
  }

  void _onNotificationAction(AppNotification notification, String action) {
    switch (action) {
      case 'mark_read':
        setState(() {
          notification.isRead = !notification.isRead;
        });
        break;
      case 'delete':
        _deleteNotification(notification);
        break;
    }
  }

  void _deleteNotification(AppNotification notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الإشعار'),
        content: const Text('هل أنت متأكد من حذف هذا الإشعار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _notifications.remove(notification);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف الإشعار')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification.isRead = true;
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديد جميع الإشعارات كمقروءة')),
    );
  }

  void _clearAllNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع الإشعارات'),
        content: const Text('هل أنت متأكد من مسح جميع الإشعارات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _notifications.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح جميع الإشعارات')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات الإشعارات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('إشعارات الطلبات'),
              subtitle: const Text('تلقي إشعارات عند وصول طلبات جديدة'),
              value: true, // This would come from settings
              onChanged: (value) {
                // Update settings
              },
            ),
            SwitchListTile(
              title: const Text('إشعارات المخزون'),
              subtitle: const Text('تلقي إشعارات عند انخفاض المخزون'),
              value: true, // This would come from settings
              onChanged: (value) {
                // Update settings
              },
            ),
            SwitchListTile(
              title: const Text('إشعارات العملاء'),
              subtitle: const Text('تلقي إشعارات عن العملاء الجدد'),
              value: false, // This would come from settings
              onChanged: (value) {
                // Update settings
              },
            ),
            SwitchListTile(
              title: const Text('إشعارات المدفوعات'),
              subtitle: const Text('تلقي إشعارات عند استلام المدفوعات'),
              value: true, // This would come from settings
              onChanged: (value) {
                // Update settings
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  List<AppNotification> _getUnreadNotifications() {
    return _notifications.where((n) => !n.isRead).toList();
  }

  List<AppNotification> _getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  int _getUnreadCount() {
    return _notifications.where((n) => !n.isRead).length;
  }
}

/// App notification model
class AppNotification {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  bool isRead;
  final DateTime createdAt;
  final Map<String, dynamic> data;

  AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.isRead,
    required this.createdAt,
    required this.data,
  });
}

/// Notification types
enum NotificationType {
  order,
  product,
  customer,
  payment,
  system,
}