import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/services/notification_service.dart';
import '../../../domain/entities/order.dart';
import '../../blocs/order/order_bloc.dart';
import '../../blocs/order/order_event.dart';
import '../../blocs/order/order_state.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';

/// Enhanced order details screen with status tracking and timeline
class OrderDetailsEnhancedScreen extends StatefulWidget {
  final String orderId;

  const OrderDetailsEnhancedScreen({
    Key? key,
    required this.orderId,
  }) : super(key: key);

  @override
  State<OrderDetailsEnhancedScreen> createState() => _OrderDetailsEnhancedScreenState();
}

class _OrderDetailsEnhancedScreenState extends State<OrderDetailsEnhancedScreen> {
  final NotificationService _notificationService = NotificationService();

  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(FetchOrderById(widget.orderId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: const Text('تفاصيل الطلب'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Share order logic
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  context.push('/edit-order/${widget.orderId}');
                  break;
                case 'duplicate':
                  _duplicateOrder();
                  break;
                case 'cancel':
                  _showCancelConfirmation();
                  break;
                case 'print':
                  _printOrder();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('تعديل'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: ListTile(
                  leading: Icon(Icons.copy),
                  title: Text('نسخ'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: ListTile(
                  leading: Icon(Icons.print),
                  title: Text('طباعة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'cancel',
                child: ListTile(
                  leading: Icon(Icons.cancel, color: Colors.red),
                  title: Text('إلغاء الطلب', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          if (state.status == OrderBlocStatus.loading) {
            return const LoadingIndicator();
          } else if (state.status == OrderBlocStatus.error) {
            return ErrorView(
              message: state.errorMessage ?? 'خطأ في تحميل تفاصيل الطلب',
              onRetry: () {
                context.read<OrderBloc>().add(FetchOrderById(widget.orderId));
              },
            );
          } else if (state.selectedOrder == null) {
            return const ErrorView(
              message: 'الطلب غير موجود',
              icon: Icons.search_off,
            );
          }

          final order = state.selectedOrder!;
          return _buildOrderDetails(order);
        },
      ),
    );
  }

  Widget _buildOrderDetails(Order order) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order header
          _buildOrderHeader(order),
          
          SizedBox(height: 24.h),
          
          // Order timeline
          _buildOrderTimeline(order),
          
          SizedBox(height: 24.h),
          
          // Customer information
          _buildCustomerInfo(order),
          
          SizedBox(height: 24.h),
          
          // Order items
          _buildOrderItems(order),
          
          SizedBox(height: 24.h),
          
          // Order summary
          _buildOrderSummary(order),
          
          SizedBox(height: 24.h),
          
          // Payment information
          _buildPaymentInfo(order),
          
          SizedBox(height: 24.h),
          
          // Notes
          if (order.notes != null && order.notes!.isNotEmpty)
            _buildNotes(order),
          
          SizedBox(height: 24.h),
          
          // Action buttons
          _buildActionButtons(order),
        ],
      ),
    );
  }

  Widget _buildOrderHeader(Order order) {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primaryContainer,
            Theme.of(context).colorScheme.primaryContainer.withOpacity(0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'طلب #${order.id.substring(0, 8)}',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'تاريخ الطلب: ${_formatDate(order.orderDate)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusChip(order.status),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          Row(
            children: [
              Expanded(
                child: _buildHeaderStat(
                  'المبلغ الإجمالي',
                  '\$${order.total.toStringAsFixed(2)}',
                  Icons.attach_money,
                ),
              ),
              Expanded(
                child: _buildHeaderStat(
                  'عدد العناصر',
                  '${order.itemCount}',
                  Icons.shopping_cart,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20.sp,
          color: Theme.of(context).colorScheme.onPrimaryContainer,
        ),
        SizedBox(width: 8.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderTimeline(Order order) {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تتبع الطلب',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          
          _buildTimelineItem(
            'تم إنشاء الطلب',
            order.orderDate,
            Icons.shopping_cart,
            isCompleted: true,
            isActive: order.status == OrderStatus.pending,
          ),
          
          _buildTimelineItem(
            'قيد المعالجة',
            order.status.index >= OrderStatus.processing.index ? order.orderDate : null,
            Icons.settings,
            isCompleted: order.status.index >= OrderStatus.processing.index,
            isActive: order.status == OrderStatus.processing,
          ),
          
          _buildTimelineItem(
            'تم الشحن',
            order.shippedDate,
            Icons.local_shipping,
            isCompleted: order.status.index >= OrderStatus.shipped.index,
            isActive: order.status == OrderStatus.shipped,
          ),
          
          _buildTimelineItem(
            'تم التسليم',
            order.deliveryDate,
            Icons.check_circle,
            isCompleted: order.status == OrderStatus.delivered,
            isActive: order.status == OrderStatus.delivered,
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(
    String title,
    DateTime? date,
    IconData icon, {
    required bool isCompleted,
    required bool isActive,
    bool isLast = false,
  }) {
    Color color;
    if (isCompleted) {
      color = Colors.green;
    } else if (isActive) {
      color = Theme.of(context).colorScheme.primary;
    } else {
      color = Colors.grey;
    }

    return Row(
      children: [
        Column(
          children: [
            Container(
              width: 40.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: isCompleted || isActive ? color : Colors.grey.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: isCompleted || isActive ? Colors.white : Colors.grey,
                size: 20.sp,
              ),
            ),
            if (!isLast)
              Container(
                width: 2.w,
                height: 30.h,
                color: isCompleted ? color : Colors.grey.withOpacity(0.3),
              ),
          ],
        ),
        
        SizedBox(width: 16.w),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isCompleted || isActive ? color : Colors.grey,
                ),
              ),
              if (date != null) ...[
                SizedBox(height: 2.h),
                Text(
                  timeago.format(date, locale: 'ar'),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerInfo(Order order) {
    return _buildSection(
      'معلومات العميل',
      Icons.person,
      Column(
        children: [
          _buildInfoRow('اسم العميل', order.customerName ?? 'غير محدد'),
          _buildInfoRow('رقم العميل', order.customerId),
          // Add more customer details here
        ],
      ),
    );
  }

  Widget _buildOrderItems(Order order) {
    return _buildSection(
      'عناصر الطلب',
      Icons.list,
      Column(
        children: order.items.map((item) => _buildOrderItem(item)).toList(),
      ),
    );
  }

  Widget _buildOrderItem(OrderItem item) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'الكمية: ${item.quantity}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  'السعر: \$${item.price.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '\$${item.total.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary(Order order) {
    return _buildSection(
      'ملخص الطلب',
      Icons.receipt,
      Column(
        children: [
          _buildSummaryRow('المجموع الفرعي', '\$${order.subtotal.toStringAsFixed(2)}'),
          _buildSummaryRow('الضريبة', '\$${order.tax.toStringAsFixed(2)}'),
          _buildSummaryRow('الشحن', '\$${order.shipping.toStringAsFixed(2)}'),
          if (order.discount > 0)
            _buildSummaryRow('الخصم', '-\$${order.discount.toStringAsFixed(2)}', isDiscount: true),
          Divider(height: 24.h),
          _buildSummaryRow(
            'المجموع الإجمالي',
            '\$${order.total.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo(Order order) {
    return _buildSection(
      'معلومات الدفع',
      Icons.payment,
      Column(
        children: [
          _buildInfoRow('حالة الدفع', 'مدفوع'), // This would come from order data
          _buildInfoRow('طريقة الدفع', 'بطاقة ائتمان'), // This would come from order data
          _buildInfoRow('رقم المرجع', 'TXN123456789'), // This would come from order data
        ],
      ),
    );
  }

  Widget _buildNotes(Order order) {
    return _buildSection(
      'ملاحظات',
      Icons.note,
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Text(
          order.notes!,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, Widget content) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          content,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false, bool isDiscount = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
                fontSize: isTotal ? 16.sp : 14.sp,
              ),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
              fontSize: isTotal ? 16.sp : 14.sp,
              color: isDiscount ? Colors.green : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Order order) {
    return Column(
      children: [
        // Primary actions
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: order.canBeCancelled ? () => _showStatusUpdateDialog(order) : null,
                icon: const Icon(Icons.update),
                label: const Text('تحديث الحالة'),
                style: ElevatedButton.styleFrom(
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _printOrder(),
                icon: const Icon(Icons.print),
                label: const Text('طباعة'),
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),
        
        // Secondary actions
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _duplicateOrder(),
                icon: const Icon(Icons.copy),
                label: const Text('نسخ الطلب'),
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: order.canBeCancelled ? () => _showCancelConfirmation() : null,
                icon: const Icon(Icons.cancel),
                label: const Text('إلغاء الطلب'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusChip(OrderStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case OrderStatus.pending:
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange;
        text = 'معلق';
        break;
      case OrderStatus.processing:
        backgroundColor = Colors.blue.withOpacity(0.1);
        textColor = Colors.blue;
        text = 'قيد المعالجة';
        break;
      case OrderStatus.shipped:
        backgroundColor = Colors.purple.withOpacity(0.1);
        textColor = Colors.purple;
        text = 'تم الشحن';
        break;
      case OrderStatus.delivered:
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green;
        text = 'تم التسليم';
        break;
      case OrderStatus.cancelled:
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red;
        text = 'ملغي';
        break;
      case OrderStatus.returned:
        backgroundColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.grey;
        text = 'مرتجع';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: textColor, width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showStatusUpdateDialog(Order order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: OrderStatus.values.map((status) {
            return ListTile(
              title: Text(_getStatusText(status)),
              leading: Radio<OrderStatus>(
                value: status,
                groupValue: order.status,
                onChanged: (value) {
                  if (value != null && value != order.status) {
                    Navigator.pop(context);
                    _updateOrderStatus(order, value);
                  }
                },
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _updateOrderStatus(Order order, OrderStatus newStatus) {
    context.read<OrderBloc>().add(UpdateOrderStatus(order.id, newStatus));
    
    _notificationService.showOrderNotification(
      orderId: order.id,
      customerName: order.customerName ?? 'عميل',
      type: _getNotificationTypeFromStatus(newStatus),
      amount: order.total,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث حالة الطلب إلى ${_getStatusText(newStatus)}'),
      ),
    );
  }

  OrderNotificationType _getNotificationTypeFromStatus(OrderStatus status) {
    switch (status) {
      case OrderStatus.processing:
        return OrderNotificationType.orderUpdated;
      case OrderStatus.shipped:
        return OrderNotificationType.orderShipped;
      case OrderStatus.delivered:
        return OrderNotificationType.orderDelivered;
      case OrderStatus.cancelled:
        return OrderNotificationType.orderCancelled;
      default:
        return OrderNotificationType.orderUpdated;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'معلق';
      case OrderStatus.processing:
        return 'قيد المعالجة';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.returned:
        return 'مرتجع';
    }
  }

  void _duplicateOrder() {
    // Navigate to add order screen with pre-filled data
    context.push('/add-order?duplicate=${widget.orderId}');
  }

  void _showCancelConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الطلب'),
        content: const Text('هل أنت متأكد من إلغاء هذا الطلب؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<OrderBloc>().add(UpdateOrderStatus(widget.orderId, OrderStatus.cancelled));
              context.pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );
  }

  void _printOrder() {
    // Print order logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة الطلب...')),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}