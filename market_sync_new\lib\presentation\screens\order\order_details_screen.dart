import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/order.dart';
import '../../blocs/order/order_bloc.dart';
import '../../blocs/order/order_event.dart';
import '../../blocs/order/order_state.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';

/// Screen to display detailed order information
class OrderDetailsScreen extends StatefulWidget {
  final String orderId;

  const OrderDetailsScreen({
    Key? key,
    required this.orderId,
  }) : super(key: key);

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(FetchOrderById(widget.orderId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: const Text('Order Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<OrderBloc>().add(FetchOrderById(widget.orderId));
            },
          ),
        ],
      ),
      body: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          if (state.status == OrderBlocStatus.loading && state.selectedOrder == null) {
            return const LoadingIndicator();
          } else if (state.status == OrderBlocStatus.error) {
            return ErrorView(
              message: state.errorMessage ?? 'Error loading order details',
              onRetry: () {
                context.read<OrderBloc>().add(FetchOrderById(widget.orderId));
              },
            );
          } else if (state.selectedOrder == null) {
            return const ErrorView(
              message: 'Order not found',
              icon: Icons.search_off,
            );
          }

          final order = state.selectedOrder!;
          return _buildOrderDetails(context, order);
        },
      ),
      bottomNavigationBar: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          final order = state.selectedOrder;
          if (order == null) return const SizedBox.shrink();
          
          if (_canUpdateStatus(order)) {
            return Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: _buildStatusUpdateRow(context, order),
            );
          }
          
          return const SizedBox.shrink();
        },
      ),
    );
  }
  
  Widget _buildOrderDetails(BuildContext context, Order order) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order header
          _buildHeaderSection(context, order),
          SizedBox(height: 24.h),
          
          // Customer information
          _buildSectionTitle(context, 'Customer Information'),
          SizedBox(height: 8.h),
          _buildCustomerInfo(context, order),
          SizedBox(height: 24.h),
          
          // Items
          _buildSectionTitle(context, 'Order Items'),
          SizedBox(height: 8.h),
          _buildOrderItems(context, order),
          SizedBox(height: 24.h),
          
          // Payment summary
          _buildSectionTitle(context, 'Payment Summary'),
          SizedBox(height: 8.h),
          _buildPaymentSummary(context, order),
          SizedBox(height: 24.h),
          
          // Additional information
          if (order.notes != null) ...[
            _buildSectionTitle(context, 'Additional Notes'),
            SizedBox(height: 8.h),
            Text(
              order.notes!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 24.h),
          ],
          
          // Delivery information
          _buildSectionTitle(context, 'Delivery Information'),
          SizedBox(height: 8.h),
          _buildDeliveryInfo(context, order),
          SizedBox(height: 32.h),
        ],
      ),
    );
  }
  
  Widget _buildHeaderSection(BuildContext context, Order order) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Order #${order.id.substring(0, 8)}',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            _buildStatusBadge(context, order.status),
          ],
        ),
        SizedBox(height: 8.h),
        Text(
          'Placed on ${DateFormat('MMMM dd, yyyy - HH:mm').format(order.orderDate)}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }
  
  Widget _buildStatusBadge(BuildContext context, OrderStatus status) {
    Color badgeColor;
    String statusText;

    switch (status) {
      case OrderStatus.pending:
        badgeColor = Colors.orange;
        statusText = 'Pending';
        break;
      case OrderStatus.processing:
        badgeColor = Colors.blue;
        statusText = 'Processing';
        break;
      case OrderStatus.shipped:
        badgeColor = Colors.indigo;
        statusText = 'Shipped';
        break;
      case OrderStatus.delivered:
        badgeColor = Colors.green;
        statusText = 'Delivered';
        break;
      case OrderStatus.cancelled:
        badgeColor = Colors.red;
        statusText = 'Cancelled';
        break;
      case OrderStatus.returned:
        badgeColor = Colors.purple;
        statusText = 'Returned';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12.w,
        vertical: 6.h,
      ),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: badgeColor,
          width: 1,
        ),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: badgeColor,
          fontWeight: FontWeight.bold,
          fontSize: 14.sp,
        ),
      ),
    );
  }
  
  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
    );
  }
  
  Widget _buildCustomerInfo(BuildContext context, Order order) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person_outline, size: 18),
                SizedBox(width: 8.w),
                Text(
                  order.customerName ?? 'Customer #${order.customerId.substring(0, 6)}',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            if (order.customer != null) ...[
              Row(
                children: [
                  const Icon(Icons.email_outlined, size: 18),
                  SizedBox(width: 8.w),
                  Text(
                    order.customer!.email,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              if (order.customer!.phoneNumber != null)
                Row(
                  children: [
                    const Icon(Icons.phone_outlined, size: 18),
                    SizedBox(width: 8.w),
                    Text(
                      order.customer!.phoneNumber!,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildOrderItems(BuildContext context, Order order) {
    return Card(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: order.items.length,
        separatorBuilder: (context, index) => Divider(height: 1.h),
        itemBuilder: (context, index) {
          final item = order.items[index];
          return ListTile(
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 8.h,
            ),
            title: Text(
              item.productName,
              style: Theme.of(context).textTheme.titleSmall,
            ),
            subtitle: Text(
              'Price: \$${item.price.toStringAsFixed(2)} × ${item.quantity}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            trailing: Text(
              '\$${item.total.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildPaymentSummary(BuildContext context, Order order) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            _buildPaymentRow(
              context, 
              'Subtotal', 
              '\$${order.subtotal.toStringAsFixed(2)}',
            ),
            SizedBox(height: 8.h),
            _buildPaymentRow(
              context, 
              'Tax', 
              '\$${order.tax.toStringAsFixed(2)}',
            ),
            SizedBox(height: 8.h),
            _buildPaymentRow(
              context, 
              'Shipping', 
              '\$${order.shipping.toStringAsFixed(2)}',
            ),
            if (order.discount > 0) ...[
              SizedBox(height: 8.h),
              _buildPaymentRow(
                context, 
                'Discount', 
                '-\$${order.discount.toStringAsFixed(2)}',
                isDiscount: true,
              ),
            ],
            SizedBox(height: 8.h),
            Divider(height: 1.h),
            SizedBox(height: 8.h),
            _buildPaymentRow(
              context, 
              'Total', 
              '\$${order.total.toStringAsFixed(2)}',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildPaymentRow(
    BuildContext context, 
    String label, 
    String value, 
    {bool isTotal = false, bool isDiscount = false}
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal
              ? Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  )
              : Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          value,
          style: isTotal
              ? Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  )
              : isDiscount
                  ? Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green,
                      )
                  : Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }
  
  Widget _buildDeliveryInfo(BuildContext context, Order order) {
    String deliveryInfo = '';
    
    if (order.status == OrderStatus.shipped && order.shippedDate != null) {
      deliveryInfo = 'Shipped on ${DateFormat('MMMM dd, yyyy').format(order.shippedDate!)}';
    } else if (order.status == OrderStatus.delivered && order.deliveryDate != null) {
      deliveryInfo = 'Delivered on ${DateFormat('MMMM dd, yyyy').format(order.deliveryDate!)}';
    } else if (order.status == OrderStatus.cancelled) {
      deliveryInfo = 'Order was cancelled';
    } else if (order.status == OrderStatus.returned) {
      deliveryInfo = 'Order was returned';
    } else if (order.status == OrderStatus.pending) {
      deliveryInfo = 'Order is pending processing';
    } else if (order.status == OrderStatus.processing) {
      deliveryInfo = 'Order is being processed';
    }
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Row(
          children: [
            Icon(
              _getDeliveryStatusIcon(order.status),
              color: _getDeliveryStatusColor(order.status),
              size: 24.r,
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Text(
                deliveryInfo,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  IconData _getDeliveryStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Icons.schedule;
      case OrderStatus.processing:
        return Icons.inventory;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
      case OrderStatus.returned:
        return Icons.assignment_return;
      default:
        return Icons.help_outline;
    }
  }
  
  Color _getDeliveryStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.processing:
        return Colors.blue;
      case OrderStatus.shipped:
        return Colors.indigo;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
      case OrderStatus.returned:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
  
  Widget _buildStatusUpdateRow(BuildContext context, Order order) {
    final List<Widget> statusButtons = [];
    
    switch (order.status) {
      case OrderStatus.pending:
        statusButtons.add(
          Expanded(
            child: _buildStatusButton(
              context,
              'Process Order',
              OrderStatus.processing,
              Colors.blue,
            ),
          ),
        );
        statusButtons.add(SizedBox(width: 16.w));
        statusButtons.add(
          Expanded(
            child: _buildStatusButton(
              context,
              'Cancel Order',
              OrderStatus.cancelled,
              Colors.red,
            ),
          ),
        );
        break;
      case OrderStatus.processing:
        statusButtons.add(
          Expanded(
            child: _buildStatusButton(
              context,
              'Mark as Shipped',
              OrderStatus.shipped,
              Colors.indigo,
            ),
          ),
        );
        statusButtons.add(SizedBox(width: 16.w));
        statusButtons.add(
          Expanded(
            child: _buildStatusButton(
              context,
              'Cancel Order',
              OrderStatus.cancelled,
              Colors.red,
            ),
          ),
        );
        break;
      case OrderStatus.shipped:
        statusButtons.add(
          Expanded(
            child: _buildStatusButton(
              context,
              'Mark as Delivered',
              OrderStatus.delivered,
              Colors.green,
            ),
          ),
        );
        break;
      case OrderStatus.delivered:
        statusButtons.add(
          Expanded(
            child: _buildStatusButton(
              context,
              'Mark as Returned',
              OrderStatus.returned,
              Colors.purple,
            ),
          ),
        );
        break;
      default:
        return const SizedBox.shrink();
    }
    
    return Row(children: statusButtons);
  }
  
  Widget _buildStatusButton(
    BuildContext context,
    String label,
    OrderStatus newStatus,
    Color color,
  ) {
    return ElevatedButton(
      onPressed: () {
        _showStatusUpdateConfirmation(context, newStatus);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 12.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
      child: Text(label),
    );
  }
  
  void _showStatusUpdateConfirmation(BuildContext context, OrderStatus newStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: Text('Are you sure you want to change the status to ${_getStatusText(newStatus)}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<OrderBloc>().add(UpdateOrderStatus(widget.orderId, newStatus));
              Navigator.of(context).pop();
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
  
  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.returned:
        return 'Returned';
      default:
        return 'Unknown';
    }
  }
  
  bool _canUpdateStatus(Order order) {
    return order.status != OrderStatus.cancelled && 
           order.status != OrderStatus.returned;
  }
}