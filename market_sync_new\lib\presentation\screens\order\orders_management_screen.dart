import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/services/notification_service.dart';
import '../../../domain/entities/order.dart';
import '../../../domain/entities/user.dart';
import '../../blocs/order/order_bloc.dart';
import '../../blocs/order/order_event.dart';
import '../../blocs/order/order_state.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_view.dart';

/// Enhanced orders management screen with status tracking and notifications
class OrdersManagementScreen extends StatefulWidget {
  const OrdersManagementScreen({Key? key}) : super(key: key);

  @override
  State<OrdersManagementScreen> createState() => _OrdersManagementScreenState();
}

class _OrdersManagementScreenState extends State<OrdersManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final NotificationService _notificationService = NotificationService();
  
  // Filter options
  OrderStatus? _selectedStatus;
  String _searchQuery = '';
  DateTimeRange? _dateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _scrollController.addListener(_onScroll);
    
    // Initialize notifications
    _notificationService.initialize();
    
    // Fetch orders when screen is first loaded
    context.read<OrderBloc>().add(const FetchOrders());
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<OrderBloc>().add(const FetchMoreOrders());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الطلبات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<OrderBloc>().add(const RefreshOrders());
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(text: 'الكل (${_getOrderCountByStatus(null)})'),
            Tab(text: 'معلق (${_getOrderCountByStatus(OrderStatus.pending)})'),
            Tab(text: 'قيد المعالجة (${_getOrderCountByStatus(OrderStatus.processing)})'),
            Tab(text: 'تم الشحن (${_getOrderCountByStatus(OrderStatus.shipped)})'),
            Tab(text: 'تم التسليم (${_getOrderCountByStatus(OrderStatus.delivered)})'),
            Tab(text: 'ملغي (${_getOrderCountByStatus(OrderStatus.cancelled)})'),
          ],
        ),
      ),
      body: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          if (state.status == OrderBlocStatus.initial) {
            return const LoadingIndicator();
          } else if (state.status == OrderBlocStatus.loading && state.orders.isEmpty) {
            return const LoadingIndicator();
          } else if (state.status == OrderBlocStatus.error) {
            return ErrorView(
              message: state.errorMessage ?? 'خطأ في تحميل الطلبات',
              onRetry: () {
                context.read<OrderBloc>().add(const FetchOrders());
              },
            );
          } else if (state.orders.isEmpty) {
            return EmptyView(
              message: 'لا توجد طلبات',
              buttonText: 'إضافة طلب',
              onActionPressed: () {
                context.push('/add-order');
              },
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildOrdersList(state.orders),
              _buildOrdersList(_filterOrdersByStatus(state.orders, OrderStatus.pending)),
              _buildOrdersList(_filterOrdersByStatus(state.orders, OrderStatus.processing)),
              _buildOrdersList(_filterOrdersByStatus(state.orders, OrderStatus.shipped)),
              _buildOrdersList(_filterOrdersByStatus(state.orders, OrderStatus.delivered)),
              _buildOrdersList(_filterOrdersByStatus(state.orders, OrderStatus.cancelled)),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/add-order');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOrdersList(List<Order> orders) {
    final filteredOrders = _applyFilters(orders);
    
    if (filteredOrders.isEmpty) {
      return const Center(
        child: Text('لا توجد طلبات تطابق المعايير المحددة'),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<OrderBloc>().add(const RefreshOrders());
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16.r),
        itemCount: filteredOrders.length,
        itemBuilder: (context, index) {
          final order = filteredOrders[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: _buildOrderCard(order),
          );
        },
      ),
    );
  }

  Widget _buildOrderCard(Order order) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () {
          context.push('/order-details/${order.id}');
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'طلب #${order.id.substring(0, 8)}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (order.customerName != null) ...[
                          SizedBox(height: 4.h),
                          Text(
                            order.customerName!,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildStatusChip(order.status),
                ],
              ),

              SizedBox(height: 12.h),

              // Order details
              Row(
                children: [
                  Expanded(
                    child: _buildOrderDetail(
                      'المبلغ الإجمالي',
                      '\$${order.total.toStringAsFixed(2)}',
                      Icons.attach_money,
                    ),
                  ),
                  Expanded(
                    child: _buildOrderDetail(
                      'عدد العناصر',
                      '${order.itemCount}',
                      Icons.shopping_cart,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 8.h),

              Row(
                children: [
                  Expanded(
                    child: _buildOrderDetail(
                      'تاريخ الطلب',
                      timeago.format(order.orderDate, locale: 'ar'),
                      Icons.access_time,
                    ),
                  ),
                  if (order.deliveryDate != null)
                    Expanded(
                      child: _buildOrderDetail(
                        'تاريخ التسليم',
                        timeago.format(order.deliveryDate!, locale: 'ar'),
                        Icons.local_shipping,
                      ),
                    ),
                ],
              ),

              SizedBox(height: 16.h),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        context.push('/order-details/${order.id}');
                      },
                      icon: const Icon(Icons.visibility),
                      label: const Text('عرض'),
                      style: OutlinedButton.styleFrom(
                        minimumSize: Size(double.infinity, 36.h),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _showStatusUpdateDialog(order);
                      },
                      icon: const Icon(Icons.update),
                      label: const Text('تحديث الحالة'),
                      style: ElevatedButton.styleFrom(
                        minimumSize: Size(double.infinity, 36.h),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderDetail(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(OrderStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case OrderStatus.pending:
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange;
        text = 'معلق';
        break;
      case OrderStatus.processing:
        backgroundColor = Colors.blue.withOpacity(0.1);
        textColor = Colors.blue;
        text = 'قيد المعالجة';
        break;
      case OrderStatus.shipped:
        backgroundColor = Colors.purple.withOpacity(0.1);
        textColor = Colors.purple;
        text = 'تم الشحن';
        break;
      case OrderStatus.delivered:
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green;
        text = 'تم التسليم';
        break;
      case OrderStatus.cancelled:
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red;
        text = 'ملغي';
        break;
      case OrderStatus.returned:
        backgroundColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.grey;
        text = 'مرتجع';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: textColor, width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showStatusUpdateDialog(Order order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: OrderStatus.values.map((status) {
            return ListTile(
              title: Text(_getStatusText(status)),
              leading: Radio<OrderStatus>(
                value: status,
                groupValue: order.status,
                onChanged: (value) {
                  if (value != null && value != order.status) {
                    Navigator.pop(context);
                    _updateOrderStatus(order, value);
                  }
                },
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _updateOrderStatus(Order order, OrderStatus newStatus) {
    // Update order status
    context.read<OrderBloc>().add(UpdateOrderStatus(order.id, newStatus));
    
    // Send notification
    _notificationService.showOrderNotification(
      orderId: order.id,
      customerName: order.customerName ?? 'عميل',
      type: _getNotificationTypeFromStatus(newStatus),
      amount: order.total,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث حالة الطلب إلى ${_getStatusText(newStatus)}'),
      ),
    );
  }

  OrderNotificationType _getNotificationTypeFromStatus(OrderStatus status) {
    switch (status) {
      case OrderStatus.processing:
        return OrderNotificationType.orderUpdated;
      case OrderStatus.shipped:
        return OrderNotificationType.orderShipped;
      case OrderStatus.delivered:
        return OrderNotificationType.orderDelivered;
      case OrderStatus.cancelled:
        return OrderNotificationType.orderCancelled;
      default:
        return OrderNotificationType.orderUpdated;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'معلق';
      case OrderStatus.processing:
        return 'قيد المعالجة';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.returned:
        return 'مرتجع';
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية الطلبات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status filter
            DropdownButtonFormField<OrderStatus?>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'الحالة',
                hintText: 'اختر الحالة',
              ),
              items: [
                const DropdownMenuItem(value: null, child: Text('جميع الحالات')),
                ...OrderStatus.values.map((status) => DropdownMenuItem(
                  value: status,
                  child: Text(_getStatusText(status)),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Date range filter
            ListTile(
              title: const Text('نطاق التاريخ'),
              subtitle: Text(_dateRange == null 
                  ? 'اختر نطاق التاريخ' 
                  : '${_dateRange!.start.day}/${_dateRange!.start.month} - ${_dateRange!.end.day}/${_dateRange!.end.month}'),
              trailing: const Icon(Icons.date_range),
              onTap: () async {
                final range = await showDateRangePicker(
                  context: context,
                  firstDate: DateTime.now().subtract(const Duration(days: 365)),
                  lastDate: DateTime.now(),
                  initialDateRange: _dateRange,
                );
                if (range != null) {
                  setState(() {
                    _dateRange = range;
                  });
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedStatus = null;
                _dateRange = null;
              });
              Navigator.pop(context);
            },
            child: const Text('مسح'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الطلبات'),
        content: TextField(
          decoration: const InputDecoration(
            labelText: 'البحث',
            hintText: 'ابحث برقم الطلب أو اسم العميل',
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
              Navigator.pop(context);
            },
            child: const Text('مسح'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  List<Order> _filterOrdersByStatus(List<Order> orders, OrderStatus status) {
    return orders.where((order) => order.status == status).toList();
  }

  List<Order> _applyFilters(List<Order> orders) {
    var filtered = orders;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((order) {
        return order.id.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (order.customerName?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply date range filter
    if (_dateRange != null) {
      filtered = filtered.where((order) {
        return order.orderDate.isAfter(_dateRange!.start) &&
               order.orderDate.isBefore(_dateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    return filtered;
  }

  int _getOrderCountByStatus(OrderStatus? status) {
    // This would normally come from the bloc state
    // For now, return a placeholder
    return 0;
  }
}