import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/order.dart';
import '../../blocs/order/order_bloc.dart';
import '../../blocs/order/order_event.dart';
import '../../blocs/order/order_state.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/order/order_card.dart';
import '../../widgets/order/order_filter_chip.dart';
import '../../widgets/order/order_search_bar.dart';

/// Screen for displaying and managing orders
class OrdersScreen extends StatefulWidget {
  const OrdersScreen({Key? key}) : super(key: key);

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    // Fetch orders when screen is first loaded
    context.read<OrderBloc>().add(const FetchOrders());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<OrderBloc>().add(const FetchMoreOrders());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppConstants.ordersTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<OrderBloc>().add(const RefreshOrders());
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter controls
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              children: [
                OrderSearchBar(
                  onSearch: (query) {
                    context.read<OrderBloc>().add(SearchOrders(query));
                  },
                  onClear: () {
                    context.read<OrderBloc>().add(const ClearOrderSearch());
                  },
                ),
                SizedBox(height: 8.h),
                SizedBox(
                  height: 40.h,
                  child: BlocBuilder<OrderBloc, OrderState>(
                    builder: (context, state) {
                      return ListView(
                        scrollDirection: Axis.horizontal,
                        children: [
                          // Status filter chips
                          OrderFilterChip(
                            label: 'All',
                            selected: state.filterStatus == null,
                            onSelected: (selected) {
                              if (selected) {
                                context.read<OrderBloc>().add(const ClearOrderFilters());
                              }
                            },
                          ),
                          SizedBox(width: 8.w),
                          OrderFilterChip(
                            label: 'Pending',
                            selected: state.filterStatus == OrderStatus.pending,
                            onSelected: (selected) {
                              if (selected) {
                                context.read<OrderBloc>().add(
                                    FilterOrdersByStatus(OrderStatus.pending));
                              } else {
                                context.read<OrderBloc>().add(const ClearOrderFilters());
                              }
                            },
                          ),
                          SizedBox(width: 8.w),
                          OrderFilterChip(
                            label: 'Processing',
                            selected: state.filterStatus == OrderStatus.processing,
                            onSelected: (selected) {
                              if (selected) {
                                context.read<OrderBloc>().add(
                                    FilterOrdersByStatus(OrderStatus.processing));
                              } else {
                                context.read<OrderBloc>().add(const ClearOrderFilters());
                              }
                            },
                          ),
                          SizedBox(width: 8.w),
                          OrderFilterChip(
                            label: 'Delivered',
                            selected: state.filterStatus == OrderStatus.delivered,
                            onSelected: (selected) {
                              if (selected) {
                                context.read<OrderBloc>().add(
                                    FilterOrdersByStatus(OrderStatus.delivered));
                              } else {
                                context.read<OrderBloc>().add(const ClearOrderFilters());
                              }
                            },
                          ),
                          SizedBox(width: 8.w),
                          OrderFilterChip(
                            label: 'Cancelled',
                            selected: state.filterStatus == OrderStatus.cancelled,
                            onSelected: (selected) {
                              if (selected) {
                                context.read<OrderBloc>().add(
                                    FilterOrdersByStatus(OrderStatus.cancelled));
                              } else {
                                context.read<OrderBloc>().add(const ClearOrderFilters());
                              }
                            },
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // Orders list
          Expanded(
            child: BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state.status == OrderBlocStatus.initial) {
                  return const LoadingIndicator();
                } else if (state.status == OrderBlocStatus.loading && state.orders.isEmpty) {
                  return const LoadingIndicator();
                } else if (state.status == OrderBlocStatus.error) {
                  return ErrorView(
                    message: state.errorMessage ?? 'Error loading orders',
                    onRetry: () {
                      context.read<OrderBloc>().add(const FetchOrders());
                    },
                  );
                } else if (state.orders.isEmpty) {
                  return EmptyView(
                    message: 'No orders found',
                    buttonText: 'Add Order',
                    onActionPressed: () {
                      context.push(AppConstants.addOrderRoute);
                    },
                  );
                }
                
                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<OrderBloc>().add(const RefreshOrders());
                  },
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.all(16.r),
                    itemCount: state.orders.length + (state.hasNextPage ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index >= state.orders.length) {
                        return Center(
                          child: Padding(
                            padding: EdgeInsets.all(16.r),
                            child: const CircularProgressIndicator(),
                          ),
                        );
                      }
                      
                      final order = state.orders[index];
                      return Padding(
                        padding: EdgeInsets.only(bottom: 12.h),
                        child: OrderCard(
                          order: order,
                          onTap: () {
                            context.read<OrderBloc>().add(SelectOrder(order));
                            context.push('${AppConstants.orderDetailsRoute}/${order.id}');
                          },
                          onStatusUpdate: (newStatus) {
                            _showStatusUpdateConfirmation(context, order, newStatus);
                          },
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push(AppConstants.addOrderRoute);
        },
        child: const Icon(Icons.add),
      ),
    );
  }
  
  void _showStatusUpdateConfirmation(BuildContext context, Order order, OrderStatus newStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: Text('Are you sure you want to change the status to ${_getStatusText(newStatus)}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<OrderBloc>().add(UpdateOrderStatus(order.id, newStatus));
              Navigator.of(context).pop();
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
  
  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.returned:
        return 'Returned';
      default:
        return 'Unknown';
    }
  }
}