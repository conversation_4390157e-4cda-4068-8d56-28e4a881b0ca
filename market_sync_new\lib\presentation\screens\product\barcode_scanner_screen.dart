import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../../core/services/barcode_service.dart';
import '../../widgets/common/custom_app_bar.dart';

/// Screen for scanning product barcodes
class BarcodeScannerScreen extends StatefulWidget {
  const BarcodeScannerScreen({Key? key}) : super(key: key);

  @override
  State<BarcodeScannerScreen> createState() => _BarcodeScannerScreenState();
}

class _BarcodeScannerScreenState extends State<BarcodeScannerScreen> {
  MobileScannerController cameraController = MobileScannerController();
  bool _isFlashOn = false;
  bool _isFrontCamera = false;
  final BarcodeService _barcodeService = BarcodeService();

  @override
  void dispose() {
    cameraController.dispose();
    super.dispose();
  }

  void _onDetect(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    for (final barcode in barcodes) {
      if (barcode.rawValue != null) {
        final String scannedValue = barcode.rawValue!;
        
        // Validate barcode
        if (_barcodeService.isValidBarcode(scannedValue)) {
          // Show success feedback
          _showBarcodeResult(scannedValue, isValid: true);
        } else {
          // Show warning for invalid barcode
          _showBarcodeResult(scannedValue, isValid: false);
        }
        break;
      }
    }
  }

  void _showBarcodeResult(String barcode, {required bool isValid}) {
    final barcodeInfo = _barcodeService.getBarcodeInfo(barcode);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isValid ? Icons.check_circle : Icons.warning,
              color: isValid ? Colors.green : Colors.orange,
            ),
            SizedBox(width: 8.w),
            Text(isValid ? 'Barcode Scanned' : 'Invalid Barcode'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Barcode: ${barcodeInfo['formatted']}'),
            Text('Type: ${barcodeInfo['type']}'),
            Text('Length: ${barcodeInfo['length']} digits'),
            if (!isValid) ...[
              SizedBox(height: 8.h),
              Text(
                'This barcode format may not be valid. Do you want to use it anyway?',
                style: TextStyle(color: Colors.orange[700]),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Scan Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.pop(barcode);
            },
            child: Text(isValid ? 'Use Barcode' : 'Use Anyway'),
          ),
        ],
      ),
    );
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
    cameraController.toggleTorch();
  }

  void _switchCamera() {
    setState(() {
      _isFrontCamera = !_isFrontCamera;
    });
    cameraController.switchCamera();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: const Text('Scan Barcode'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera view
          MobileScanner(
            controller: cameraController,
            onDetect: _onDetect,
          ),
          
          // Overlay with scanning frame
          _buildScanningOverlay(),
          
          // Control buttons
          _buildControlButtons(),
          
          // Instructions
          _buildInstructions(),
        ],
      ),
    );
  }

  Widget _buildScanningOverlay() {
    return Container(
      decoration: ShapeDecoration(
        shape: _ScannerOverlayShape(),
      ),
    );
  }

  Widget _buildControlButtons() {
    return Positioned(
      bottom: 100.h,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Flash toggle
          _buildControlButton(
            icon: _isFlashOn ? Icons.flash_on : Icons.flash_off,
            label: 'Flash',
            onTap: _toggleFlash,
          ),
          
          // Switch camera
          _buildControlButton(
            icon: Icons.flip_camera_ios,
            label: 'Switch',
            onTap: _switchCamera,
          ),
          
          // Manual entry
          _buildControlButton(
            icon: Icons.keyboard,
            label: 'Manual',
            onTap: () {
              _showManualEntryDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 24.sp,
            ),
            SizedBox(height: 4.h),
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      top: 100.h,
      left: 0,
      right: 0,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 32.w),
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          'Position the barcode within the frame to scan',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  void _showManualEntryDialog() {
    final TextEditingController controller = TextEditingController();
    String? validationMessage;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Enter Barcode Manually'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: 'Barcode',
                  hintText: 'Enter barcode number',
                  errorText: validationMessage,
                ),
                keyboardType: TextInputType.text,
                onChanged: (value) {
                  setState(() {
                    if (value.isNotEmpty) {
                      final isValid = _barcodeService.isValidBarcode(value);
                      if (!isValid) {
                        validationMessage = 'Invalid barcode format';
                      } else {
                        validationMessage = null;
                      }
                    } else {
                      validationMessage = null;
                    }
                  });
                },
              ),
              if (controller.text.isNotEmpty && validationMessage == null) ...[
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.green, size: 16),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: Text(
                          'Valid ${_barcodeService.getBarcodeType(controller.text).name} barcode',
                          style: const TextStyle(color: Colors.green, fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (controller.text.isNotEmpty) {
                  Navigator.of(context).pop();
                  context.pop(controller.text);
                }
              },
              child: const Text('OK'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Custom shape for the scanner overlay
class _ScannerOverlayShape extends ShapeBorder {
  const _ScannerOverlayShape();

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path path = Path()..addRect(rect);
    
    // Create the scanning window
    final scanArea = Rect.fromCenter(
      center: rect.center,
      width: rect.width * 0.8,
      height: rect.height * 0.3,
    );
    
    path.addRRect(RRect.fromRectAndRadius(scanArea, const Radius.circular(12)));
    
    return path;
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final paint = Paint()
      ..color = Colors.black54
      ..style = PaintingStyle.fill;

    canvas.drawPath(getOuterPath(rect), paint);
    
    // Draw corner indicators
    final scanArea = Rect.fromCenter(
      center: rect.center,
      width: rect.width * 0.8,
      height: rect.height * 0.3,
    );
    
    _drawCornerIndicators(canvas, scanArea);
  }

  void _drawCornerIndicators(Canvas canvas, Rect scanArea) {
    final paint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    const cornerLength = 20.0;
    
    // Top-left corner
    canvas.drawLine(
      Offset(scanArea.left, scanArea.top + cornerLength),
      Offset(scanArea.left, scanArea.top),
      paint,
    );
    canvas.drawLine(
      Offset(scanArea.left, scanArea.top),
      Offset(scanArea.left + cornerLength, scanArea.top),
      paint,
    );
    
    // Top-right corner
    canvas.drawLine(
      Offset(scanArea.right - cornerLength, scanArea.top),
      Offset(scanArea.right, scanArea.top),
      paint,
    );
    canvas.drawLine(
      Offset(scanArea.right, scanArea.top),
      Offset(scanArea.right, scanArea.top + cornerLength),
      paint,
    );
    
    // Bottom-left corner
    canvas.drawLine(
      Offset(scanArea.left, scanArea.bottom - cornerLength),
      Offset(scanArea.left, scanArea.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(scanArea.left, scanArea.bottom),
      Offset(scanArea.left + cornerLength, scanArea.bottom),
      paint,
    );
    
    // Bottom-right corner
    canvas.drawLine(
      Offset(scanArea.right - cornerLength, scanArea.bottom),
      Offset(scanArea.right, scanArea.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(scanArea.right, scanArea.bottom - cornerLength),
      Offset(scanArea.right, scanArea.bottom),
      paint,
    );
  }

  @override
  ShapeBorder scale(double t) => _ScannerOverlayShape();
}