import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:uuid/uuid.dart';

import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/product.dart';
import '../../../domain/entities/category.dart';
import '../../blocs/product/product_bloc.dart';
import '../../blocs/product/product_event.dart';
import '../../blocs/product/product_state.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/product/image_picker_widget.dart';
import 'barcode_scanner_screen.dart';

/// Screen for adding a new product or editing an existing one
class ProductFormScreen extends StatefulWidget {
  final String? productId;

  const ProductFormScreen({
    Key? key,
    this.productId,
  }) : super(key: key);

  @override
  State<ProductFormScreen> createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends State<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _discountPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _imageUrlController = TextEditingController();
  
  String _selectedCategoryId = '';
  bool _isActive = true;
  bool _isEditing = false;
  File? _selectedImage;
  
  @override
  void initState() {
    super.initState();
    _isEditing = widget.productId != null;
    
    if (_isEditing) {
      context.read<ProductBloc>().add(FetchProductById(widget.productId!));
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _discountPriceController.dispose();
    _stockController.dispose();
    _barcodeController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }
  
  void _populateForm(Product product) {
    _nameController.text = product.name;
    _descriptionController.text = product.description;
    _priceController.text = product.price.toString();
    _discountPriceController.text = product.discountPrice?.toString() ?? '';
    _stockController.text = product.stockQuantity.toString();
    _barcodeController.text = product.barcode;
    _imageUrlController.text = product.imageUrl ?? '';
    _selectedCategoryId = product.categoryId;
    _isActive = product.isActive;
  }

  Future<void> _scanBarcode() async {
    try {
      final result = await context.push('/barcode-scanner');
      if (result != null && result is String) {
        setState(() {
          _barcodeController.text = result;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error scanning barcode: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  void _saveProduct() {
    if (_formKey.currentState!.validate()) {
      final double price = double.parse(_priceController.text);
      final double? discountPrice = _discountPriceController.text.isNotEmpty
          ? double.parse(_discountPriceController.text)
          : null;
      
      if (discountPrice != null && discountPrice >= price) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Discount price must be less than regular price'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
      
      final Product product = _isEditing
          ? _createUpdatedProduct()
          : _createNewProduct();
      
      if (_isEditing) {
        context.read<ProductBloc>().add(UpdateProduct(product));
      } else {
        context.read<ProductBloc>().add(CreateProduct(product));
      }
      
      context.pop();
    }
  }
  
  Product _createNewProduct() {
    final now = DateTime.now();
    return Product(
      id: const Uuid().v4(),
      name: _nameController.text,
      description: _descriptionController.text,
      price: double.parse(_priceController.text),
      discountPrice: _discountPriceController.text.isNotEmpty
          ? double.parse(_discountPriceController.text)
          : null,
      stockQuantity: int.parse(_stockController.text),
      categoryId: _selectedCategoryId,
      imageUrl: _imageUrlController.text.isNotEmpty
          ? _imageUrlController.text
          : null,
      barcode: _barcodeController.text,
      isActive: _isActive,
      createdAt: now,
      updatedAt: now,
    );
  }
  
  Product _createUpdatedProduct() {
    final state = context.read<ProductBloc>().state;
    final currentProduct = state.selectedProduct!;
    
    return currentProduct.copyWith(
      name: _nameController.text,
      description: _descriptionController.text,
      price: double.parse(_priceController.text),
      discountPrice: _discountPriceController.text.isNotEmpty
          ? double.parse(_discountPriceController.text)
          : null,
      stockQuantity: int.parse(_stockController.text),
      categoryId: _selectedCategoryId,
      imageUrl: _imageUrlController.text.isNotEmpty
          ? _imageUrlController.text
          : null,
      barcode: _barcodeController.text,
      isActive: _isActive,
      updatedAt: DateTime.now(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text(_isEditing ? 'Edit Product' : 'Add Product'),
      ),
      body: _isEditing
          ? BlocConsumer<ProductBloc, ProductState>(
              listener: (context, state) {
                if (state.status == ProductStatus.productUpdated) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Product updated successfully')),
                  );
                }
              },
              builder: (context, state) {
                if (state.status == ProductStatus.loading && state.selectedProduct == null) {
                  return const LoadingIndicator();
                } else if (state.status == ProductStatus.error) {
                  return ErrorView(
                    message: state.errorMessage ?? 'Error loading product',
                    onRetry: () {
                      context.read<ProductBloc>().add(FetchProductById(widget.productId!));
                    },
                  );
                } else if (state.selectedProduct == null) {
                  return const ErrorView(
                    message: 'Product not found',
                    icon: Icons.search_off,
                  );
                }
                
                // Populate form only once when product is loaded
                if (_nameController.text.isEmpty) {
                  _populateForm(state.selectedProduct!);
                }
                
                return _buildForm(context);
              },
            )
          : _buildForm(context),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, -3),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _saveProduct,
          style: ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, 50.h),
          ),
          child: Text(_isEditing ? 'Update Product' : 'Save Product'),
        ),
      ),
    );
  }
  
  Widget _buildForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Product Name*',
                hintText: 'Enter product name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a product name';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            
            // Product description
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description*',
                hintText: 'Enter product description',
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a product description';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            
            // Price and discount price
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _priceController,
                    decoration: const InputDecoration(
                      labelText: 'Price*',
                      hintText: 'Enter price',
                      prefixText: '\$',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a price';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Please enter a valid price';
                      }
                      if (double.parse(value) <= 0) {
                        return 'Price must be greater than 0';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: TextFormField(
                    controller: _discountPriceController,
                    decoration: const InputDecoration(
                      labelText: 'Discount Price',
                      hintText: 'Enter discount price',
                      prefixText: '\$',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid price';
                        }
                        if (double.parse(value) <= 0) {
                          return 'Price must be greater than 0';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            // Stock quantity
            TextFormField(
              controller: _stockController,
              decoration: const InputDecoration(
                labelText: 'Stock Quantity*',
                hintText: 'Enter stock quantity',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter stock quantity';
                }
                if (int.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            
            // Barcode with scanner
            TextFormField(
              controller: _barcodeController,
              decoration: InputDecoration(
                labelText: 'Barcode*',
                hintText: 'Enter barcode or scan',
                suffixIcon: IconButton(
                  icon: const Icon(Icons.qr_code_scanner),
                  onPressed: _scanBarcode,
                  tooltip: 'Scan Barcode',
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a barcode';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            
            // Image picker widget
            ImagePickerWidget(
              initialImageUrl: _imageUrlController.text,
              onImageSelected: (file) {
                setState(() {
                  _selectedImage = file;
                });
              },
              onImageUrlChanged: (url) {
                _imageUrlController.text = url ?? '';
              },
            ),
            SizedBox(height: 16.h),
            
            // Category dropdown with manage button
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedCategoryId.isNotEmpty ? _selectedCategoryId : null,
                    decoration: const InputDecoration(
                      labelText: 'Category*',
                      hintText: 'Select category',
                    ),
                    items: [
                      // Mock categories - in real app, fetch from bloc
                      const DropdownMenuItem(
                        value: '1',
                        child: Text('Electronics'),
                      ),
                      const DropdownMenuItem(
                        value: '2',
                        child: Text('Clothing'),
                      ),
                      const DropdownMenuItem(
                        value: '3',
                        child: Text('Home & Garden'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedCategoryId = value;
                        });
                      }
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a category';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 8.w),
                IconButton(
                  onPressed: () {
                    context.push('/categories');
                  },
                  icon: const Icon(Icons.settings),
                  tooltip: 'Manage Categories',
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            // Active status
            SwitchListTile(
              title: const Text('Active'),
              subtitle: const Text('Product will be visible to customers'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }
}