import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/product.dart';
import '../../blocs/product/product_bloc.dart';
import '../../blocs/product/product_event.dart';
import '../../blocs/product/product_state.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/product/product_card.dart';
import '../../widgets/product/product_search_bar.dart';
import '../../widgets/product/product_filter_chip.dart';

/// Screen for displaying and managing products
class ProductsScreen extends StatefulWidget {
  const ProductsScreen({Key? key}) : super(key: key);

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    // Fetch products when screen is first loaded
    context.read<ProductBloc>().add(const FetchProducts());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<ProductBloc>().add(const FetchMoreProducts());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppConstants.productsTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<ProductBloc>().add(const RefreshProducts());
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter controls
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              children: [
                ProductSearchBar(
                  onSearch: (query) {
                    context.read<ProductBloc>().add(SearchProducts(query));
                  },
                  onClear: () {
                    context.read<ProductBloc>().add(const ClearSearch());
                  },
                ),
                SizedBox(height: 8.h),
                SizedBox(
                  height: 40.h,
                  child: BlocBuilder<ProductBloc, ProductState>(
                    builder: (context, state) {
                      return ListView(
                        scrollDirection: Axis.horizontal,
                        children: [
                          // Add filter chips for categories here
                          ProductFilterChip(
                            label: 'All',
                            selected: state.filterCategory == null,
                            onSelected: (selected) {
                              if (selected) {
                                context.read<ProductBloc>().add(const ClearFilter());
                              }
                            },
                          ),
                          SizedBox(width: 8.w),
                          // In a real app, you'd fetch categories and dynamically build these
                          ProductFilterChip(
                            label: 'Category 1',
                            selected: state.filterCategory == 'category1',
                            onSelected: (selected) {
                              if (selected) {
                                context.read<ProductBloc>().add(
                                    const FilterProductsByCategory('category1'));
                              } else {
                                context.read<ProductBloc>().add(const ClearFilter());
                              }
                            },
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // Products list
          Expanded(
            child: BlocBuilder<ProductBloc, ProductState>(
              builder: (context, state) {
                if (state.status == ProductStatus.initial) {
                  return const LoadingIndicator();
                } else if (state.status == ProductStatus.loading && state.products.isEmpty) {
                  return const LoadingIndicator();
                } else if (state.status == ProductStatus.error) {
                  return ErrorView(
                    message: state.errorMessage ?? 'Error loading products',
                    onRetry: () {
                      context.read<ProductBloc>().add(const FetchProducts());
                    },
                  );
                } else if (state.products.isEmpty) {
                  return EmptyView(
                    message: 'No products found',
                    buttonText: 'Add Product',
                    onActionPressed: () {
                      context.push(AppConstants.addProductRoute);
                    },
                  );
                }
                
                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<ProductBloc>().add(const RefreshProducts());
                  },
                  child: GridView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.all(16.r),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.75,
                      crossAxisSpacing: 16.w,
                      mainAxisSpacing: 16.h,
                    ),
                    itemCount: state.products.length + (state.hasNextPage ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index >= state.products.length) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      
                      final product = state.products[index];
                      return ProductCard(
                        product: product,
                        onTap: () {
                          context.read<ProductBloc>().add(SelectProduct(product));
                          context.push('${AppConstants.productDetailsRoute}/${product.id}');
                        },
                        onEdit: () {
                          context.push('${AppConstants.editProductRoute}/${product.id}');
                        },
                        onDelete: () {
                          _showDeleteConfirmation(context, product);
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push(AppConstants.addProductRoute);
        },
        child: const Icon(Icons.add),
      ),
    );
  }
  
  void _showDeleteConfirmation(BuildContext context, Product product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: Text('Are you sure you want to delete ${product.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<ProductBloc>().add(DeleteProduct(product.id));
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}