import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../domain/entities/enhanced_user.dart';
import '../../../domain/entities/role.dart';
import '../../widgets/common/custom_app_bar.dart';

/// Profile screen for displaying and editing user information
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // Mock user data - in real app, this would come from a bloc
  late EnhancedUser _currentUser;

  @override
  void initState() {
    super.initState();
    _initializeMockUser();
  }

  void _initializeMockUser() {
    _currentUser = EnhancedUser(
      id: 'user_001',
      email: '<EMAIL>',
      name: 'أحمد محمد السالم',
      phone: '+966501234567',
      avatar: null,
      role: SystemRoles.owner,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      lastLoginAt: DateTime.now().subtract(const Duration(minutes: 30)),
      preferences: {
        'theme_mode': 'system',
        'language': 'ar',
        'notifications_enabled': true,
        'order_notifications': true,
        'product_notifications': true,
        'customer_notifications': false,
      },
      settings: {
        'two_factor_enabled': false,
        'biometric_enabled': true,
        'auto_logout': 30,
        'session_timeout': 120,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: const Text('الملف الشخصي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              context.push('/edit-profile');
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              context.push('/settings');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            // Profile header
            _buildProfileHeader(),
            
            SizedBox(height: 24.h),
            
            // Quick stats
            _buildQuickStats(),
            
            SizedBox(height: 24.h),
            
            // Profile sections
            _buildPersonalInfo(),
            
            SizedBox(height: 16.h),
            
            _buildRoleAndPermissions(),
            
            SizedBox(height: 16.h),
            
            _buildAccountStatus(),
            
            SizedBox(height: 16.h),
            
            _buildActivityInfo(),
            
            SizedBox(height: 24.h),
            
            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: EdgeInsets.all(24.r),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primaryContainer,
            Theme.of(context).colorScheme.primaryContainer.withOpacity(0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // Avatar
          Stack(
            children: [
              CircleAvatar(
                radius: 50.r,
                backgroundColor: Theme.of(context).colorScheme.primary,
                backgroundImage: _currentUser.avatar != null 
                    ? NetworkImage(_currentUser.avatar!)
                    : null,
                child: _currentUser.avatar == null
                    ? Text(
                        _currentUser.initials,
                        style: TextStyle(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Theme.of(context).colorScheme.surface,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    size: 16.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // Name and role
          Text(
            _currentUser.displayName,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          
          SizedBox(height: 4.h),
          
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Text(
              _currentUser.role.name,
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
              ),
            ),
          ),
          
          SizedBox(height: 8.h),
          
          Text(
            _currentUser.email,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'انضم منذ',
            timeago.format(_currentUser.createdAt, locale: 'ar'),
            Icons.calendar_today,
            Colors.blue,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: _buildStatCard(
            'آخر نشاط',
            _currentUser.lastLoginAt != null 
                ? timeago.format(_currentUser.lastLoginAt!, locale: 'ar')
                : 'لم يسجل دخول',
            Icons.access_time,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfo() {
    return _buildSection(
      'المعلومات الشخصية',
      Icons.person,
      Column(
        children: [
          _buildInfoRow('الاسم الكامل', _currentUser.name),
          _buildInfoRow('البريد الإلكتروني', _currentUser.email),
          _buildInfoRow('رقم الهاتف', _currentUser.phone ?? 'غير محدد'),
          _buildInfoRow('معرف المستخدم', _currentUser.id),
        ],
      ),
    );
  }

  Widget _buildRoleAndPermissions() {
    return _buildSection(
      'الدور والصلاحيات',
      Icons.security,
      Column(
        children: [
          _buildInfoRow('الدور', _currentUser.role.name),
          _buildInfoRow('الوصف', _currentUser.role.description),
          _buildInfoRow('عدد الصلاحيات', '${_currentUser.role.permissions.length}'),
          SizedBox(height: 12.h),
          Align(
            alignment: Alignment.centerLeft,
            child: TextButton.icon(
              onPressed: () {
                _showPermissionsDialog();
              },
              icon: const Icon(Icons.list),
              label: const Text('عرض جميع الصلاحيات'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountStatus() {
    return _buildSection(
      'حالة الحساب',
      Icons.account_circle,
      Column(
        children: [
          _buildStatusRow(
            'الحساب نشط',
            _currentUser.isActive,
            _currentUser.isActive ? Colors.green : Colors.red,
          ),
          _buildStatusRow(
            'البريد الإلكتروني مؤكد',
            _currentUser.isEmailVerified,
            _currentUser.isEmailVerified ? Colors.green : Colors.orange,
          ),
          _buildStatusRow(
            'رقم الهاتف مؤكد',
            _currentUser.isPhoneVerified,
            _currentUser.isPhoneVerified ? Colors.green : Colors.orange,
          ),
          _buildStatusRow(
            'المصادقة الثنائية',
            _currentUser.twoFactorEnabled,
            _currentUser.twoFactorEnabled ? Colors.green : Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityInfo() {
    return _buildSection(
      'معلومات النشاط',
      Icons.timeline,
      Column(
        children: [
          _buildInfoRow(
            'تاريخ الإنشاء',
            '${_currentUser.createdAt.day}/${_currentUser.createdAt.month}/${_currentUser.createdAt.year}',
          ),
          _buildInfoRow(
            'آخر تحديث',
            '${_currentUser.updatedAt.day}/${_currentUser.updatedAt.month}/${_currentUser.updatedAt.year}',
          ),
          if (_currentUser.lastLoginAt != null)
            _buildInfoRow(
              'آخر تسجيل دخول',
              '${_currentUser.lastLoginAt!.day}/${_currentUser.lastLoginAt!.month}/${_currentUser.lastLoginAt!.year} - ${_currentUser.lastLoginAt!.hour}:${_currentUser.lastLoginAt!.minute.toString().padLeft(2, '0')}',
            ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, Widget content) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          content,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status, Color color) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  status ? Icons.check_circle : Icons.cancel,
                  size: 16.sp,
                  color: color,
                ),
                SizedBox(width: 4.w),
                Text(
                  status ? 'نعم' : 'لا',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Primary actions
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  context.push('/edit-profile');
                },
                icon: const Icon(Icons.edit),
                label: const Text('تعديل الملف الشخصي'),
                style: ElevatedButton.styleFrom(
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  context.push('/change-password');
                },
                icon: const Icon(Icons.lock),
                label: const Text('تغيير كلمة المرور'),
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),
        
        // Secondary actions
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  context.push('/settings');
                },
                icon: const Icon(Icons.settings),
                label: const Text('الإعدادات'),
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  _showLogoutConfirmation();
                },
                icon: const Icon(Icons.logout),
                label: const Text('تسجيل الخروج'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  minimumSize: Size(double.infinity, 48.h),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showPermissionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('صلاحيات المستخدم'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _currentUser.role.permissions.length,
            itemBuilder: (context, index) {
              final permission = _currentUser.role.permissions[index];
              return ListTile(
                leading: Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 20.sp,
                ),
                title: Text(permission.name),
                subtitle: Text(permission.description),
                dense: true,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Perform logout
              context.go('/login');
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}