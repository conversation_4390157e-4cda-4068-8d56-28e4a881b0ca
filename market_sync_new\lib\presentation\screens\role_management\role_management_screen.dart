import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../domain/entities/role.dart';
import '../../../domain/entities/permission.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/empty_view.dart';

/// Screen for managing roles and permissions
class RoleManagementScreen extends StatefulWidget {
  const RoleManagementScreen({Key? key}) : super(key: key);

  @override
  State<RoleManagementScreen> createState() => _RoleManagementScreenState();
}

class _RoleManagementScreenState extends State<RoleManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  // Mock data - in real app, this would come from a bloc
  List<Role> _roles = [];
  List<Permission> _permissions = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeMockData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeMockData() {
    _roles = SystemRoles.allSystemRoles;
    _permissions = SystemPermissions.allPermissions;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأدوار والصلاحيات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showCreateRoleDialog,
            tooltip: 'إضافة دور جديد',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: 'مساعدة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الأدوار', icon: Icon(Icons.people)),
            Tab(text: 'الصلاحيات', icon: Icon(Icons.security)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRolesTab(),
          _buildPermissionsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateRoleDialog,
        child: const Icon(Icons.add),
        tooltip: 'إضافة دور جديد',
      ),
    );
  }

  Widget _buildRolesTab() {
    if (_roles.isEmpty) {
      return const EmptyView(
        message: 'لا توجد أدوار',
        buttonText: 'إضافة دور',
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: _roles.length,
      itemBuilder: (context, index) {
        final role = _roles[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: _buildRoleCard(role),
        );
      },
    );
  }

  Widget _buildPermissionsTab() {
    final groupedPermissions = _groupPermissionsByCategory();
    
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: groupedPermissions.keys.length,
      itemBuilder: (context, index) {
        final category = groupedPermissions.keys.elementAt(index);
        final permissions = groupedPermissions[category]!;
        
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: _buildPermissionCategoryCard(category, permissions),
        );
      },
    );
  }

  Widget _buildRoleCard(Role role) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () => _showRoleDetailsDialog(role),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Role header
              Row(
                children: [
                  Container(
                    width: 48.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                      color: _getRoleColor(role).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getRoleIcon(role),
                      color: _getRoleColor(role),
                      size: 24.sp,
                    ),
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                role.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            if (role.isSystemRole)
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Text(
                                  'نظام',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          role.description,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  PopupMenuButton<String>(
                    onSelected: (value) => _onRoleAction(role, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: ListTile(
                          leading: Icon(Icons.visibility),
                          title: Text('عرض التفاصيل'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      if (!role.isSystemRole) ...[
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('تعديل'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: ListTile(
                            leading: Icon(Icons.copy),
                            title: Text('نسخ'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف', style: TextStyle(color: Colors.red)),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // Role stats
              Row(
                children: [
                  Expanded(
                    child: _buildRoleStat(
                      'الصلاحيات',
                      '${role.permissions.length}',
                      Icons.security,
                      Colors.blue,
                    ),
                  ),
                  Expanded(
                    child: _buildRoleStat(
                      'المستخدمين',
                      '0', // This would come from user count
                      Icons.people,
                      Colors.green,
                    ),
                  ),
                  Expanded(
                    child: _buildRoleStat(
                      'الحالة',
                      role.isActive ? 'نشط' : 'معطل',
                      role.isActive ? Icons.check_circle : Icons.cancel,
                      role.isActive ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleStat(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20.sp),
        SizedBox(height: 4.h),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionCategoryCard(PermissionCategory category, List<Permission> permissions) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: ExpansionTile(
        leading: Icon(_getCategoryIcon(category)),
        title: Text(
          category.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text('${permissions.length} صلاحية'),
        children: permissions.map((permission) => _buildPermissionTile(permission)).toList(),
      ),
    );
  }

  Widget _buildPermissionTile(Permission permission) {
    return ListTile(
      leading: Icon(
        Icons.check_circle,
        color: permission.isActive ? Colors.green : Colors.grey,
        size: 20.sp,
      ),
      title: Text(permission.name),
      subtitle: Text(permission.description),
      trailing: permission.isActive 
          ? const Icon(Icons.check, color: Colors.green)
          : const Icon(Icons.close, color: Colors.red),
      dense: true,
    );
  }

  Map<PermissionCategory, List<Permission>> _groupPermissionsByCategory() {
    final Map<PermissionCategory, List<Permission>> grouped = {};
    
    for (final permission in _permissions) {
      if (!grouped.containsKey(permission.category)) {
        grouped[permission.category] = [];
      }
      grouped[permission.category]!.add(permission);
    }
    
    return grouped;
  }

  Color _getRoleColor(Role role) {
    switch (role.id) {
      case 'super_admin':
        return Colors.red;
      case 'owner':
        return Colors.purple;
      case 'manager':
        return Colors.blue;
      case 'employee':
        return Colors.green;
      case 'sales_rep':
        return Colors.orange;
      case 'customer':
        return Colors.grey;
      case 'accountant':
        return Colors.teal;
      default:
        return Colors.blue;
    }
  }

  IconData _getRoleIcon(Role role) {
    switch (role.id) {
      case 'super_admin':
        return Icons.admin_panel_settings;
      case 'owner':
        return Icons.business;
      case 'manager':
        return Icons.manage_accounts;
      case 'employee':
        return Icons.person;
      case 'sales_rep':
        return Icons.sell;
      case 'customer':
        return Icons.person_outline;
      case 'accountant':
        return Icons.account_balance;
      default:
        return Icons.person;
    }
  }

  IconData _getCategoryIcon(PermissionCategory category) {
    switch (category) {
      case PermissionCategory.products:
        return Icons.inventory_2;
      case PermissionCategory.orders:
        return Icons.shopping_cart;
      case PermissionCategory.customers:
        return Icons.people;
      case PermissionCategory.inventory:
        return Icons.warehouse;
      case PermissionCategory.reports:
        return Icons.analytics;
      case PermissionCategory.settings:
        return Icons.settings;
      case PermissionCategory.users:
        return Icons.admin_panel_settings;
      case PermissionCategory.financial:
        return Icons.account_balance;
    }
  }

  void _onRoleAction(Role role, String action) {
    switch (action) {
      case 'view':
        _showRoleDetailsDialog(role);
        break;
      case 'edit':
        _showEditRoleDialog(role);
        break;
      case 'duplicate':
        _duplicateRole(role);
        break;
      case 'delete':
        _showDeleteRoleDialog(role);
        break;
    }
  }

  void _showRoleDetailsDialog(Role role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(role.name),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('الوصف: ${role.description}'),
              SizedBox(height: 8.h),
              Text('عدد الصلاحيات: ${role.permissions.length}'),
              SizedBox(height: 8.h),
              Text('نوع الدور: ${role.isSystemRole ? 'دور نظام' : 'دور مخصص'}'),
              SizedBox(height: 8.h),
              Text('الحالة: ${role.isActive ? 'نشط' : 'معطل'}'),
              SizedBox(height: 16.h),
              const Text('الصلاحيات:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8.h),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: role.permissions.length,
                  itemBuilder: (context, index) {
                    final permission = role.permissions[index];
                    return ListTile(
                      leading: Icon(Icons.check, color: Colors.green, size: 16.sp),
                      title: Text(permission.name),
                      subtitle: Text(permission.description),
                      dense: true,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          if (!role.isSystemRole)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showEditRoleDialog(role);
              },
              child: const Text('تعديل'),
            ),
        ],
      ),
    );
  }

  void _showCreateRoleDialog() {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    List<Permission> selectedPermissions = [];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إنشاء دور جديد'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الدور',
                    hintText: 'أدخل اسم الدور',
                  ),
                ),
                SizedBox(height: 16.h),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    hintText: 'أدخل وصف الدور',
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 16.h),
                const Text('اختر الصلاحيات:', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 8.h),
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _permissions.length,
                    itemBuilder: (context, index) {
                      final permission = _permissions[index];
                      final isSelected = selectedPermissions.contains(permission);
                      
                      return CheckboxListTile(
                        title: Text(permission.name),
                        subtitle: Text(permission.description),
                        value: isSelected,
                        onChanged: (value) {
                          setState(() {
                            if (value == true) {
                              selectedPermissions.add(permission);
                            } else {
                              selectedPermissions.remove(permission);
                            }
                          });
                        },
                        dense: true,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (nameController.text.isNotEmpty && selectedPermissions.isNotEmpty) {
                  _createRole(nameController.text, descriptionController.text, selectedPermissions);
                  Navigator.pop(context);
                }
              },
              child: const Text('إنشاء'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditRoleDialog(Role role) {
    final TextEditingController nameController = TextEditingController(text: role.name);
    final TextEditingController descriptionController = TextEditingController(text: role.description);
    List<Permission> selectedPermissions = List.from(role.permissions);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('تعديل الدور'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الدور',
                  ),
                ),
                SizedBox(height: 16.h),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 16.h),
                const Text('الصلاحيات:', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 8.h),
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _permissions.length,
                    itemBuilder: (context, index) {
                      final permission = _permissions[index];
                      final isSelected = selectedPermissions.contains(permission);
                      
                      return CheckboxListTile(
                        title: Text(permission.name),
                        subtitle: Text(permission.description),
                        value: isSelected,
                        onChanged: (value) {
                          setState(() {
                            if (value == true) {
                              selectedPermissions.add(permission);
                            } else {
                              selectedPermissions.remove(permission);
                            }
                          });
                        },
                        dense: true,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                _updateRole(role, nameController.text, descriptionController.text, selectedPermissions);
                Navigator.pop(context);
              },
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteRoleDialog(Role role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الدور'),
        content: Text('هل أنت متأكد من حذف دور "${role.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _deleteRole(role);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة - إدارة الأدوار'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الأدوار:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('• تحدد الأدوار ما يمكن للمستخدمين فعله في النظام'),
            Text('• كل دور يحتوي على مجموعة من الصلاحيات'),
            Text('• أدوار النظام لا يمكن تعديلها أو حذفها'),
            SizedBox(height: 16),
            Text('الصلاحيات:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('• تحدد الإجراءات المحددة التي يمكن تنفيذها'),
            Text('• مجمعة حسب الفئات لسهولة الإدارة'),
            Text('• يمكن تخصيص الصلاحيات لكل دور'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _createRole(String name, String description, List<Permission> permissions) {
    final newRole = Role(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      permissions: permissions,
      isSystemRole: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    setState(() {
      _roles.add(newRole);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم إنشاء دور "$name" بنجاح')),
    );
  }

  void _updateRole(Role role, String name, String description, List<Permission> permissions) {
    final updatedRole = role.copyWith(
      name: name,
      description: description,
      permissions: permissions,
      updatedAt: DateTime.now(),
    );

    setState(() {
      final index = _roles.indexWhere((r) => r.id == role.id);
      if (index != -1) {
        _roles[index] = updatedRole;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم تحديث دور "$name" بنجاح')),
    );
  }

  void _duplicateRole(Role role) {
    final duplicatedRole = Role(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: '${role.name} (نسخة)',
      description: role.description,
      permissions: List.from(role.permissions),
      isSystemRole: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    setState(() {
      _roles.add(duplicatedRole);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم نسخ دور "${role.name}" بنجاح')),
    );
  }

  void _deleteRole(Role role) {
    setState(() {
      _roles.removeWhere((r) => r.id == role.id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم حذف دور "${role.name}" بنجاح')),
    );
  }
}