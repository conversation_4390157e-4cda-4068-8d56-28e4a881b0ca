import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../domain/entities/enhanced_user.dart';
import '../../../domain/entities/role.dart';
import '../../blocs/theme/theme_bloc.dart';
import '../../blocs/theme/theme_event.dart';
import '../../blocs/language/language_bloc.dart';
import '../../blocs/language/language_event.dart';
import '../../widgets/common/custom_app_bar.dart';

/// Settings screen for app configuration and user preferences
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // Mock user data - in real app, this would come from a bloc
  late EnhancedUser _currentUser;

  @override
  void initState() {
    super.initState();
    _initializeMockUser();
  }

  void _initializeMockUser() {
    _currentUser = EnhancedUser(
      id: 'user_001',
      email: '<EMAIL>',
      name: 'أحمد محمد السالم',
      phone: '+966501234567',
      avatar: null,
      role: SystemRoles.owner,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      lastLoginAt: DateTime.now().subtract(const Duration(minutes: 30)),
      preferences: {
        'theme_mode': 'system',
        'language': 'ar',
        'notifications_enabled': true,
        'order_notifications': true,
        'product_notifications': true,
        'customer_notifications': false,
        'sound_enabled': true,
        'vibration_enabled': true,
      },
      settings: {
        'two_factor_enabled': false,
        'biometric_enabled': true,
        'auto_logout': 30,
        'session_timeout': 120,
        'data_sync': true,
        'offline_mode': false,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: const Text('الإعدادات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _showResetSettingsDialog,
            tooltip: 'إعادة تعيين الإعدادات',
          ),
        ],
      ),
      body: ListView(
        padding: EdgeInsets.all(16.r),
        children: [
          // User info header
          _buildUserHeader(),
          
          SizedBox(height: 24.h),
          
          // Appearance settings
          _buildAppearanceSection(),
          
          SizedBox(height: 16.h),
          
          // Notification settings
          _buildNotificationSection(),
          
          SizedBox(height: 16.h),
          
          // Security settings
          _buildSecuritySection(),
          
          SizedBox(height: 16.h),
          
          // Data and sync settings
          _buildDataSyncSection(),
          
          SizedBox(height: 16.h),
          
          // System settings (only for admins)
          if (_currentUser.isAdmin) ...[
            _buildSystemSection(),
            SizedBox(height: 16.h),
          ],
          
          // About and support
          _buildAboutSection(),
          
          SizedBox(height: 24.h),
          
          // Danger zone
          _buildDangerZone(),
        ],
      ),
    );
  }

  Widget _buildUserHeader() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30.r,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: Text(
              _currentUser.initials,
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentUser.displayName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _currentUser.role.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              context.push('/profile');
            },
            icon: const Icon(Icons.arrow_forward_ios),
          ),
        ],
      ),
    );
  }

  Widget _buildAppearanceSection() {
    return _buildSection(
      'المظهر',
      Icons.palette,
      [
        _buildThemeSelector(),
        _buildLanguageSelector(),
        _buildFontSizeSelector(),
      ],
    );
  }

  Widget _buildNotificationSection() {
    return _buildSection(
      'الإشعارات',
      Icons.notifications,
      [
        _buildSwitchTile(
          'تفعيل الإشعارات',
          'تلقي إشعارات من التطبيق',
          _currentUser.notificationsEnabled,
          (value) => _updatePreference('notifications_enabled', value),
        ),
        _buildSwitchTile(
          'إشعارات الطلبات',
          'تلقي إشعارات عند وصول طلبات جديدة',
          _currentUser.orderNotificationsEnabled,
          (value) => _updatePreference('order_notifications', value),
          enabled: _currentUser.notificationsEnabled,
        ),
        _buildSwitchTile(
          'إشعارات المنتجات',
          'تلقي إشعارات عن المخزون والمنتجات',
          _currentUser.productNotificationsEnabled,
          (value) => _updatePreference('product_notifications', value),
          enabled: _currentUser.notificationsEnabled,
        ),
        _buildSwitchTile(
          'إشعارات العملاء',
          'تلقي إشعارات عن العملاء الجدد',
          _currentUser.customerNotificationsEnabled,
          (value) => _updatePreference('customer_notifications', value),
          enabled: _currentUser.notificationsEnabled,
        ),
        _buildSwitchTile(
          'الصوت',
          'تشغيل أصوات الإشعارات',
          _currentUser.getPreference<bool>('sound_enabled', true) ?? true,
          (value) => _updatePreference('sound_enabled', value),
          enabled: _currentUser.notificationsEnabled,
        ),
        _buildSwitchTile(
          'الاهتزاز',
          'تفعيل الاهتزاز مع الإشعارات',
          _currentUser.getPreference<bool>('vibration_enabled', true) ?? true,
          (value) => _updatePreference('vibration_enabled', value),
          enabled: _currentUser.notificationsEnabled,
        ),
      ],
    );
  }

  Widget _buildSecuritySection() {
    return _buildSection(
      'الأمان',
      Icons.security,
      [
        _buildSwitchTile(
          'المصادقة الثنائية',
          'تفعيل المصادقة الثنائية لحماية إضافية',
          _currentUser.twoFactorEnabled,
          (value) => _updateSetting('two_factor_enabled', value),
        ),
        _buildSwitchTile(
          'المصادقة البيومترية',
          'استخدام بصمة الإصبع أو الوجه',
          _currentUser.biometricEnabled,
          (value) => _updateSetting('biometric_enabled', value),
        ),
        _buildListTile(
          'تغيير كلمة المرور',
          'تحديث كلمة المرور الخاصة بك',
          Icons.lock,
          () => context.push('/change-password'),
        ),
        _buildAutoLogoutSelector(),
        _buildSessionTimeoutSelector(),
      ],
    );
  }

  Widget _buildDataSyncSection() {
    return _buildSection(
      'البيانات والمزامنة',
      Icons.sync,
      [
        _buildSwitchTile(
          'مزامنة البيانات',
          'مزامنة البيانات مع الخادم تلقائياً',
          _currentUser.getSetting<bool>('data_sync', true) ?? true,
          (value) => _updateSetting('data_sync', value),
        ),
        _buildSwitchTile(
          'الوضع غير المتصل',
          'السماح بالعمل بدون اتصال بالإنترنت',
          _currentUser.getSetting<bool>('offline_mode', false) ?? false,
          (value) => _updateSetting('offline_mode', value),
        ),
        _buildListTile(
          'مسح ذاكرة التخزين المؤقت',
          'حذف البيانات المؤقتة لتوفير مساحة',
          Icons.cleaning_services,
          _showClearCacheDialog,
        ),
        _buildListTile(
          'تصدير البيانات',
          'تصدير بياناتك الشخصية',
          Icons.download,
          _showExportDataDialog,
        ),
      ],
    );
  }

  Widget _buildSystemSection() {
    return _buildSection(
      'إعدادات النظام',
      Icons.admin_panel_settings,
      [
        _buildListTile(
          'إدارة المستخدمين',
          'إضافة وإدارة المستخدمين',
          Icons.people,
          () => context.push('/user-management'),
        ),
        _buildListTile(
          'إدارة الأدوار',
          'تخصيص الأدوار والصلاحيات',
          Icons.security,
          () => context.push('/role-management'),
        ),
        _buildListTile(
          'النسخ الاحتياطي',
          'إنشاء واستعادة النسخ الاحتياطية',
          Icons.backup,
          () => context.push('/backup-management'),
        ),
        _buildListTile(
          'سجلات النظام',
          'عرض سجلات النشاط والأخطاء',
          Icons.list_alt,
          () => context.push('/system-logs'),
        ),
      ],
    );
  }

  Widget _buildAboutSection() {
    return _buildSection(
      'حول التطبيق',
      Icons.info,
      [
        _buildListTile(
          'معلومات التطبيق',
          'الإصدار والمطور',
          Icons.info_outline,
          _showAppInfoDialog,
        ),
        _buildListTile(
          'الدعم الفني',
          'تواصل مع فريق الدعم',
          Icons.support_agent,
          _showSupportDialog,
        ),
        _buildListTile(
          'الشروط والأحكام',
          'اقرأ شروط الاستخدام',
          Icons.description,
          () => context.push('/terms'),
        ),
        _buildListTile(
          'سياسة الخصوصية',
          'كيف نحمي بياناتك',
          Icons.privacy_tip,
          () => context.push('/privacy'),
        ),
      ],
    );
  }

  Widget _buildDangerZone() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: Colors.red.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.warning, color: Colors.red, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'منطقة الخطر',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildDangerButton(
            'إعادة تعيين الإعدادات',
            'استعادة الإعدادات الافتراضية',
            Icons.restore,
            _showResetSettingsDialog,
          ),
          SizedBox(height: 8.h),
          _buildDangerButton(
            'حذف الحساب',
            'حذف حسابك نهائياً',
            Icons.delete_forever,
            _showDeleteAccountDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Row(
              children: [
                Icon(icon, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged, {
    bool enabled = true,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: enabled ? onChanged : null,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
    );
  }

  Widget _buildListTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
    );
  }

  Widget _buildDangerButton(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.red),
      title: Text(title, style: const TextStyle(color: Colors.red)),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, color: Colors.red),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildThemeSelector() {
    return ListTile(
      leading: const Icon(Icons.brightness_6),
      title: const Text('المظهر'),
      subtitle: Text(_getThemeDisplayName(_currentUser.themeMode)),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: _showThemeDialog,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
    );
  }

  Widget _buildLanguageSelector() {
    return ListTile(
      leading: const Icon(Icons.language),
      title: const Text('اللغة'),
      subtitle: Text(_getLanguageDisplayName(_currentUser.language)),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: _showLanguageDialog,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
    );
  }

  Widget _buildFontSizeSelector() {
    return ListTile(
      leading: const Icon(Icons.text_fields),
      title: const Text('حجم الخط'),
      subtitle: const Text('متوسط'),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: _showFontSizeDialog,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
    );
  }

  Widget _buildAutoLogoutSelector() {
    final autoLogout = _currentUser.getSetting<int>('auto_logout', 30) ?? 30;
    return ListTile(
      leading: const Icon(Icons.timer),
      title: const Text('تسجيل الخروج التلقائي'),
      subtitle: Text('$autoLogout دقيقة'),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: _showAutoLogoutDialog,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
    );
  }

  Widget _buildSessionTimeoutSelector() {
    final sessionTimeout = _currentUser.getSetting<int>('session_timeout', 120) ?? 120;
    return ListTile(
      leading: const Icon(Icons.schedule),
      title: const Text('انتهاء الجلسة'),
      subtitle: Text('$sessionTimeout دقيقة'),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: _showSessionTimeoutDialog,
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
    );
  }

  String _getThemeDisplayName(String themeMode) {
    switch (themeMode) {
      case 'light':
        return 'فاتح';
      case 'dark':
        return 'داكن';
      case 'system':
        return 'تلقائي';
      default:
        return 'تلقائي';
    }
  }

  String _getLanguageDisplayName(String language) {
    switch (language) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return 'العربية';
    }
  }

  void _updatePreference(String key, dynamic value) {
    setState(() {
      _currentUser = _currentUser.updatePreference(key, value);
    });
  }

  void _updateSetting(String key, dynamic value) {
    setState(() {
      _currentUser = _currentUser.updateSetting(key, value);
    });
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر المظهر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('فاتح'),
              value: 'light',
              groupValue: _currentUser.themeMode,
              onChanged: (value) {
                if (value != null) {
                  _updatePreference('theme_mode', value);
                  final themeMode = value == 'light' ? ThemeMode.light :
                                   value == 'dark' ? ThemeMode.dark : ThemeMode.system;
                  context.read<ThemeBloc>().add(ChangeThemeEvent(themeMode));
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('داكن'),
              value: 'dark',
              groupValue: _currentUser.themeMode,
              onChanged: (value) {
                if (value != null) {
                  _updatePreference('theme_mode', value);
                  final themeMode = value == 'light' ? ThemeMode.light :
                                   value == 'dark' ? ThemeMode.dark : ThemeMode.system;
                  context.read<ThemeBloc>().add(ChangeThemeEvent(themeMode));
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('تلقائي'),
              value: 'system',
              groupValue: _currentUser.themeMode,
              onChanged: (value) {
                if (value != null) {
                  _updatePreference('theme_mode', value);
                  final themeMode = value == 'light' ? ThemeMode.light :
                                   value == 'dark' ? ThemeMode.dark : ThemeMode.system;
                  context.read<ThemeBloc>().add(ChangeThemeEvent(themeMode));
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: _currentUser.language,
              onChanged: (value) {
                if (value != null) {
                  _updatePreference('language', value);
                  final locale = Locale(value);
                  context.read<LanguageBloc>().add(ChangeLanguageEvent(locale));
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: _currentUser.language,
              onChanged: (value) {
                if (value != null) {
                  _updatePreference('language', value);
                  final locale = Locale(value);
                  context.read<LanguageBloc>().add(ChangeLanguageEvent(locale));
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: const Text('هذه الميزة ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showAutoLogoutDialog() {
    final options = [15, 30, 60, 120, 0]; // 0 means disabled
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج التلقائي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((minutes) {
            return RadioListTile<int>(
              title: Text(minutes == 0 ? 'معطل' : '$minutes دقيقة'),
              value: minutes,
              groupValue: _currentUser.getSetting<int>('auto_logout', 30),
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('auto_logout', value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showSessionTimeoutDialog() {
    final options = [60, 120, 240, 480]; // minutes
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('انتهاء الجلسة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((minutes) {
            return RadioListTile<int>(
              title: Text('$minutes دقيقة'),
              value: minutes,
              groupValue: _currentUser.getSetting<int>('session_timeout', 120),
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('session_timeout', value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح ذاكرة التخزين المؤقت'),
        content: const Text('هل تريد حذف جميع البيانات المؤقتة؟ هذا سيحرر مساحة تخزين.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Perform cache clearing
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح ذاكرة التخزين المؤقت')),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showExportDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: const Text('سيتم إرسال ملف يحتوي على بياناتك الشخصية إلى بريدك الإلكتروني.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Perform data export
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('جاري تصدير البيانات...')),
              );
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  void _showAppInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('MarketSync'),
            Text('الإصدار: 1.0.0'),
            Text('المطور: Kilo Code'),
            Text('تاريخ الإصدار: 2025'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showSupportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الدعم الفني'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('للحصول على المساعدة:'),
            SizedBox(height: 8),
            Text('البريد الإلكتروني: <EMAIL>'),
            Text('الهاتف: +966 50 123 4567'),
            Text('ساعات العمل: 9 صباحاً - 6 مساءً'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showResetSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset settings
              setState(() {
                _currentUser = _currentUser.copyWith(
                  preferences: {},
                  settings: {},
                  updatedAt: DateTime.now(),
                );
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إعادة تعيين الإعدادات')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Text('تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بياناتك نهائياً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Show confirmation dialog
              _showFinalDeleteConfirmation();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showFinalDeleteConfirmation() {
    final TextEditingController confirmController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد حذف الحساب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اكتب "حذف" للتأكيد:'),
            TextField(
              controller: confirmController,
              decoration: const InputDecoration(
                hintText: 'حذف',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (confirmController.text == 'حذف') {
                Navigator.pop(context);
                // Perform account deletion
                context.go('/login');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف نهائياً'),
          ),
        ],
      ),
    );
  }
}