import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A customized button widget
class CustomButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String text;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final double? borderRadius;
  final IconData? icon;
  final bool isLoading;

  const CustomButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.borderRadius,
    this.icon,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Default button style
    final ButtonStyle buttonStyle = isOutlined
        ? OutlinedButton.styleFrom(
            foregroundColor: textColor ?? colorScheme.primary,
            side: BorderSide(
              color: backgroundColor ?? colorScheme.primary,
              width: 1.5,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12.r),
            ),
            padding: EdgeInsets.symmetric(
              vertical: 16.h,
              horizontal: 24.w,
            ),
            minimumSize: Size(width ?? double.infinity, height ?? 56.h),
          ).copyWith(
            overlayColor: MaterialStateProperty.resolveWith(
              (states) => colorScheme.primary.withOpacity(0.1),
            ),
          )
        : ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? colorScheme.primary,
            foregroundColor: textColor ?? colorScheme.onPrimary,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12.r),
            ),
            padding: EdgeInsets.symmetric(
              vertical: 16.h,
              horizontal: 24.w,
            ),
            minimumSize: Size(width ?? double.infinity, height ?? 56.h),
          );

    Widget child;
    
    // Handle loading state
    if (isLoading) {
      child = SizedBox(
        height: 24.h,
        width: 24.h,
        child: CircularProgressIndicator(
          strokeWidth: 2.5,
          color: isOutlined ? colorScheme.primary : colorScheme.onPrimary,
        ),
      );
    } else {
      // Handle icon+text or text only
      if (icon != null) {
        child = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20.w),
            SizedBox(width: 8.w),
            Text(
              text,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );
      } else {
        child = Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        );
      }
    }

    // Return appropriate button based on isOutlined
    return isOutlined
        ? OutlinedButton(
            style: buttonStyle,
            onPressed: isLoading ? null : onPressed,
            child: child,
          )
        : ElevatedButton(
            style: buttonStyle,
            onPressed: isLoading ? null : onPressed,
            child: child,
          );
  }
}