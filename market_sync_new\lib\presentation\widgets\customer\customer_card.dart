import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/user.dart';

/// Card widget to display customer information
class CustomerCard extends StatelessWidget {
  final User customer;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const CustomerCard({
    Key? key,
    required this.customer,
    this.onTap,
    this.onEdit,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCustomerHeader(context),
              SizedBox(height: 12.h),
              _buildCustomerDetails(context),
              if (onEdit != null || onDelete != null) ...[
                const Spacer(),
                _buildActionButtons(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerHeader(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Customer avatar
        Container(
          width: 50.r,
          height: 50.r,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: customer.imageUrl != null
              ? ClipOval(
                  child: Image.network(
                    customer.imageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Center(
                        child: Icon(
                          Icons.person,
                          size: 30.r,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      );
                    },
                  ),
                )
              : Center(
                  child: Icon(
                    Icons.person,
                    size: 30.r,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
        ),
        SizedBox(width: 16.w),
        // Customer name and role
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                customer.name,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              Text(
                _getRoleText(customer.role),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getRoleColor(context, customer.role),
                    ),
              ),
            ],
          ),
        ),
        // Active status indicator
        Chip(
          label: Text(
            customer.isActive ? 'Active' : 'Inactive',
            style: TextStyle(
              fontSize: 12.sp,
              color: customer.isActive
                  ? Colors.green
                  : Theme.of(context).colorScheme.error,
            ),
          ),
          backgroundColor: customer.isActive
              ? Colors.green.withOpacity(0.1)
              : Theme.of(context).colorScheme.error.withOpacity(0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
            side: BorderSide(
              color: customer.isActive
                  ? Colors.green
                  : Theme.of(context).colorScheme.error,
              width: 1,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Email
        Row(
          children: [
            Icon(
              Icons.email_outlined,
              size: 16.r,
              color: Colors.grey,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                customer.email,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        // Phone number
        if (customer.phoneNumber != null) ...[
          Row(
            children: [
              Icon(
                Icons.phone_outlined,
                size: 16.r,
                color: Colors.grey,
              ),
              SizedBox(width: 8.w),
              Text(
                customer.phoneNumber!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 8.h),
        ],
        // Customer since
        Row(
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 16.r,
              color: Colors.grey,
            ),
            SizedBox(width: 8.w),
            Text(
              'Customer since: ${_formatDate(customer.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (onEdit != null)
          IconButton(
            onPressed: onEdit,
            icon: const Icon(Icons.edit_outlined),
            tooltip: 'Edit',
            constraints: const BoxConstraints(),
            padding: EdgeInsets.all(4.r),
            iconSize: 20.r,
          ),
        if (onDelete != null) ...[
          SizedBox(width: 8.w),
          IconButton(
            onPressed: onDelete,
            icon: const Icon(Icons.delete_outline),
            tooltip: 'Delete',
            constraints: const BoxConstraints(),
            padding: EdgeInsets.all(4.r),
            iconSize: 20.r,
            color: Theme.of(context).colorScheme.error,
          ),
        ],
      ],
    );
  }

  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.owner:
        return 'Owner';
      case UserRole.employee:
        return 'Employee';
      case UserRole.customer:
        return 'Retail Customer';
      case UserRole.wholesaler:
        return 'Wholesaler';
      case UserRole.agent:
        return 'Agent';
      case UserRole.admin:
        return 'Administrator';
      default:
        return 'Unknown';
    }
  }

  Color _getRoleColor(BuildContext context, UserRole role) {
    switch (role) {
      case UserRole.owner:
        return Colors.purple;
      case UserRole.employee:
        return Colors.blue;
      case UserRole.customer:
        return Colors.green;
      case UserRole.wholesaler:
        return Colors.orange;
      case UserRole.agent:
        return Colors.teal;
      case UserRole.admin:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}