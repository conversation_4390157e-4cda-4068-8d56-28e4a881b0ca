import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget to display recent activity in the dashboard
class RecentActivity extends StatelessWidget {
  const RecentActivity({super.key});

  @override
  Widget build(BuildContext context) {
    // TODO: Replace with actual data from backend
    final activities = [
      _ActivityItem(
        icon: Icons.shopping_cart,
        title: 'New Order #1234',
        subtitle: 'Order placed by <PERSON>',
        time: '5 mins ago',
        color: Colors.blue,
      ),
      _ActivityItem(
        icon: Icons.inventory,
        title: 'Low Stock Alert',
        subtitle: 'Product X is running low',
        time: '15 mins ago',
        color: Colors.orange,
      ),
      _ActivityItem(
        icon: Icons.person_add,
        title: 'New Customer',
        subtitle: 'Mohammed registered',
        time: '1 hour ago',
        color: Colors.green,
      ),
      _ActivityItem(
        icon: Icons.payment,
        title: 'Payment Received',
        subtitle: 'Order #1230 paid',
        time: '2 hours ago',
        color: Colors.purple,
      ),
    ];

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: ListView.separated(
        padding: EdgeInsets.all(16.w),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: activities.length,
        separatorBuilder: (context, index) => Divider(height: 16.h),
        itemBuilder: (context, index) => activities[index],
      ),
    );
  }
}

class _ActivityItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final String time;
  final Color color;

  const _ActivityItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.time,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24.w,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ),
        ),
        Text(
          time,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
        ),
      ],
    );
  }
}