import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/order.dart';

/// Card widget to display order information
class OrderCard extends StatelessWidget {
  final Order order;
  final VoidCallback? onTap;
  final Function(OrderStatus)? onStatusUpdate;

  const OrderCard({
    Key? key,
    required this.order,
    this.onTap,
    this.onStatusUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOrderHeader(context),
              SizedBox(height: 12.h),
              _buildOrderDetails(context),
              SizedBox(height: 12.h),
              _buildOrderStatus(context),
              if (onStatusUpdate != null && _canUpdateStatus())
                SizedBox(height: 12.h),
              if (onStatusUpdate != null && _canUpdateStatus())
                _buildStatusUpdateButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Order #${order.id.substring(0, 8)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              Text(
                DateFormat('MMM dd, yyyy - HH:mm').format(order.orderDate),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
        _buildStatusBadge(context),
      ],
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    Color badgeColor;
    String statusText;

    switch (order.status) {
      case OrderStatus.pending:
        badgeColor = Colors.orange;
        statusText = 'Pending';
        break;
      case OrderStatus.processing:
        badgeColor = Colors.blue;
        statusText = 'Processing';
        break;
      case OrderStatus.shipped:
        badgeColor = Colors.indigo;
        statusText = 'Shipped';
        break;
      case OrderStatus.delivered:
        badgeColor = Colors.green;
        statusText = 'Delivered';
        break;
      case OrderStatus.cancelled:
        badgeColor = Colors.red;
        statusText = 'Cancelled';
        break;
      case OrderStatus.returned:
        badgeColor = Colors.purple;
        statusText = 'Returned';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12.w,
        vertical: 6.h,
      ),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: badgeColor,
          width: 1,
        ),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: badgeColor,
          fontWeight: FontWeight.bold,
          fontSize: 12.sp,
        ),
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Customer:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    order.customerName ?? 'Customer #${order.customerId.substring(0, 6)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Items:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    '${order.itemCount} items',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Subtotal:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    '\$${order.subtotal.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Total:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    '\$${order.total.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderStatus(BuildContext context) {
    String deliveryInfo = '';
    
    if (order.status == OrderStatus.shipped && order.shippedDate != null) {
      deliveryInfo = 'Shipped on ${DateFormat('MMM dd, yyyy').format(order.shippedDate!)}';
    } else if (order.status == OrderStatus.delivered && order.deliveryDate != null) {
      deliveryInfo = 'Delivered on ${DateFormat('MMM dd, yyyy').format(order.deliveryDate!)}';
    } else if (order.status == OrderStatus.cancelled) {
      deliveryInfo = 'Order was cancelled';
    } else if (order.status == OrderStatus.returned) {
      deliveryInfo = 'Order was returned';
    }
    
    return deliveryInfo.isNotEmpty 
        ? Text(
            deliveryInfo,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontStyle: FontStyle.italic,
                ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildStatusUpdateButtons(BuildContext context) {
    List<Widget> buttons = [];
    
    switch (order.status) {
      case OrderStatus.pending:
        buttons.add(_buildStatusButton(
          context,
          'Process',
          OrderStatus.processing,
          Colors.blue,
        ));
        buttons.add(SizedBox(width: 8.w));
        buttons.add(_buildStatusButton(
          context,
          'Cancel',
          OrderStatus.cancelled,
          Colors.red,
        ));
        break;
      case OrderStatus.processing:
        buttons.add(_buildStatusButton(
          context,
          'Ship',
          OrderStatus.shipped,
          Colors.indigo,
        ));
        buttons.add(SizedBox(width: 8.w));
        buttons.add(_buildStatusButton(
          context,
          'Cancel',
          OrderStatus.cancelled,
          Colors.red,
        ));
        break;
      case OrderStatus.shipped:
        buttons.add(_buildStatusButton(
          context,
          'Deliver',
          OrderStatus.delivered,
          Colors.green,
        ));
        break;
      case OrderStatus.delivered:
        buttons.add(_buildStatusButton(
          context,
          'Return',
          OrderStatus.returned,
          Colors.purple,
        ));
        break;
      default:
        return const SizedBox.shrink();
    }
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: buttons,
    );
  }

  Widget _buildStatusButton(
    BuildContext context,
    String label,
    OrderStatus newStatus,
    Color color,
  ) {
    return ElevatedButton(
      onPressed: () {
        if (onStatusUpdate != null) {
          onStatusUpdate!(newStatus);
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
          side: BorderSide(color: color),
        ),
        elevation: 0,
      ),
      child: Text(label),
    );
  }

  bool _canUpdateStatus() {
    return order.status != OrderStatus.cancelled && 
           order.status != OrderStatus.returned;
  }
}