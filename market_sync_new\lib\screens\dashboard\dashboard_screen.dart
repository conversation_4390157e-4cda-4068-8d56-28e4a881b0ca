import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../l10n/app_localizations.dart';
import '../../widgets/dashboard/dashboard_card.dart';
import '../../widgets/dashboard/recent_activity.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.dashboard),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.person_outline),
            onPressed: () {
              // TODO: Navigate to profile
            },
          ),
        ],
      ),
      body: SafeArea(
        child: ListView(
          padding: EdgeInsets.all(16.w),
          children: [
            Text(
              'Welcome back!',
              style: Theme.of(context).textTheme.displayMedium,
            ),
            SizedBox(height: 24.h),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 16.h,
              crossAxisSpacing: 16.w,
              childAspectRatio: 1.2,
              children: [
                DashboardCard(
                  title: l10n.orders,
                  value: '12',
                  icon: Icons.shopping_cart_outlined,
                  color: Colors.blue,
                  onTap: () {
                    // TODO: Navigate to orders
                  },
                ),
                DashboardCard(
                  title: l10n.products,
                  value: '156',
                  icon: Icons.inventory_2_outlined,
                  color: Colors.green,
                  onTap: () {
                    // TODO: Navigate to products
                  },
                ),
                DashboardCard(
                  title: l10n.customers,
                  value: '48',
                  icon: Icons.people_outline,
                  color: Colors.orange,
                  onTap: () {
                    // TODO: Navigate to customers
                  },
                ),
                DashboardCard(
                  title: l10n.sales,
                  value: '\$2,450',
                  icon: Icons.attach_money,
                  color: Colors.purple,
                  onTap: () {
                    // TODO: Navigate to sales
                  },
                ),
              ],
            ),
            SizedBox(height: 32.h),
            Text(
              l10n.reports,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            const RecentActivity(),
          ],
        ),
      ),
      drawer: Drawer(
        child: SafeArea(
          child: ListView(
            padding: EdgeInsets.zero,
            children: [
              Container(
                padding: EdgeInsets.all(16.w),
                color: Theme.of(context).primaryColor,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 30.r,
                      backgroundColor: Colors.white,
                      child: Icon(Icons.person, size: 40.r),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'John Doe',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                          ),
                    ),
                    Text(
                      'Owner',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white70,
                          ),
                    ),
                  ],
                ),
              ),
              ListTile(
                leading: const Icon(Icons.dashboard_outlined),
                title: Text(l10n.dashboard),
                onTap: () {
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.inventory_2_outlined),
                title: Text(l10n.inventory),
                onTap: () {
                  // TODO: Navigate to inventory
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.shopping_cart_outlined),
                title: Text(l10n.orders),
                onTap: () {
                  // TODO: Navigate to orders
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.settings_outlined),
                title: Text(l10n.settings),
                onTap: () {
                  // TODO: Navigate to settings
                  Navigator.pop(context);
                },
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.logout),
                title: Text(l10n.logout),
                onTap: () {
                  // TODO: Implement logout
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}