name: market_sync
description: "Market Sync Flutter Application"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  get_it: ^7.6.0
  dartz: ^0.10.1
  
  # Routing
  go_router: ^13.2.0
  
  # Network & Storage
  dio: ^5.4.0
  shared_preferences: ^2.2.2
  cached_network_image: ^3.3.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  supabase_flutter: ^2.3.3
  
  # UI Utilities
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.9
  shimmer: ^3.0.0
  intl: ^0.20.2
  lottie: ^2.6.0
  
  # Icons
  cupertino_icons: ^1.0.8
  font_awesome_flutter: ^10.7.0
  
  # Charts and Data Visualization
  fl_chart: ^0.66.2
  
  # Utils
  logger: ^2.0.2
  url_launcher: ^6.2.5
  image_picker: ^1.0.7
  path_provider: ^2.1.2
  connectivity_plus: ^5.0.2
  permission_handler: ^11.0.1
  device_info_plus: ^9.1.1
  
  # Barcode Scanner
  mobile_scanner: ^3.5.6
  
  # UUID Generator
  uuid: ^4.3.3
  
  # Notifications
  flutter_local_notifications: ^17.2.3
  
  # Date/Time utilities
  timeago: ^3.6.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.8
  hive_generator: ^2.0.1
  mockito: ^5.4.2
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.2

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/translations/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700